#!/bin/bash

# Netbird utility functions for health checks, debugging, and connectivity testing

# Function to check Netbird network connectivity
check_netbird_connectivity() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    echo "Checking Netbird connectivity..."
    echo "==============================="
    
    # Check if management API is accessible
    echo "Testing Management API..."
    if curl -s -k https://localhost:443/api/status >/dev/null 2>&1; then
        echo -e "${GREEN}Management API: Accessible${NC}"
    else
        echo -e "${RED}Management API: Not accessible${NC}"
    fi
    
    # Check if signal service is accessible
    echo "Testing Signal service..."
    if netstat -tuln | grep -q ":33073"; then
        echo -e "${GREEN}Signal service: Port 33073 is open${NC}"
    else
        echo -e "${RED}Signal service: Port 33073 is not accessible${NC}"
    fi
    
    # Check if dashboard is accessible
    echo "Testing Dashboard..."
    if curl -s http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}Dashboard: Accessible${NC}"
    else
        echo -e "${RED}Dashboard: Not accessible${NC}"
    fi
    
    # Check TURN server
    echo "Testing TURN server..."
    if netstat -tuln | grep -q ":3478"; then
        echo -e "${GREEN}TURN server: Port 3478 is open${NC}"
    else
        echo -e "${RED}TURN server: Port 3478 is not accessible${NC}"
    fi
}

# Function to perform Netbird health check
netbird_health_check() {
    echo "Netbird Health Check"
    echo "==================="
    
    local health_score=0
    local max_score=10
    
    # Check if installation is detected
    echo -n "Installation detection: "
    if detect_netbird_installation >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Found${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Not found${NC}"
    fi
    
    # Check if services are running
    echo -n "Services running: "
    if is_netbird_running; then
        echo -e "${GREEN}✓ Running${NC}"
        ((health_score += 3))
    else
        echo -e "${RED}✗ Not running${NC}"
    fi
    
    # Check connectivity
    echo -n "Management API: "
    if curl -s -k https://localhost:443/api/status >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Accessible${NC}"
        ((health_score += 2))
    else
        echo -e "${RED}✗ Not accessible${NC}"
    fi
    
    echo -n "Signal service: "
    if netstat -tuln | grep -q ":33073"; then
        echo -e "${GREEN}✓ Port open${NC}"
        ((health_score += 2))
    else
        echo -e "${RED}✗ Port not open${NC}"
    fi
    
    echo -n "Dashboard: "
    if curl -s http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Accessible${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Not accessible${NC}"
    fi
    
    echo -n "TURN server: "
    if netstat -tuln | grep -q ":3478"; then
        echo -e "${GREEN}✓ Port open${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Port not open${NC}"
    fi
    
    echo ""
    echo "Health Score: $health_score/$max_score"
    
    if [ $health_score -ge 8 ]; then
        echo -e "${GREEN}Status: Excellent${NC}"
    elif [ $health_score -ge 6 ]; then
        echo -e "${YELLOW}Status: Good${NC}"
    elif [ $health_score -ge 4 ]; then
        echo -e "${YELLOW}Status: Fair - Some issues detected${NC}"
    else
        echo -e "${RED}Status: Poor - Multiple issues detected${NC}"
    fi
    
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to debug Netbird installation
debug_netbird_installation() {
    echo "Netbird Installation Debug"
    echo "========================="
    
    local netbird_path=$(detect_netbird_installation)
    
    if [ -n "$netbird_path" ]; then
        echo "✓ Installation found at: $netbird_path"
        echo ""
        
        cd "$netbird_path"
        
        # Check configuration files
        echo "Configuration files:"
        for file in docker-compose.yml management.json turnserver.conf Caddyfile zitadel.env dashboard.env; do
            if [ -f "$file" ]; then
                echo "  ✓ $file"
            else
                echo "  ✗ $file (missing)"
            fi
        done
        
        echo ""
        echo "Docker compose services:"
        if [ -f "docker-compose.yml" ]; then
            docker compose config --services 2>/dev/null || echo "Error reading docker-compose.yml"
        fi
        
        echo ""
        echo "Container status:"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep -E "management|signal|dashboard|coturn|relay|zitadel|caddy" || echo "No Netbird containers found"
        
        echo ""
        echo "Docker volumes:"
        docker volume ls | grep -E "netbird|zitadel" || echo "No Netbird volumes found"
        
        echo ""
        echo "Network ports:"
        netstat -tuln | grep -E ":80 |:443 |:33073 |:3478 |:10000 " || echo "No Netbird ports found listening"
        
    else
        echo "✗ Netbird installation not detected."
        echo ""
        echo "Searching for potential installations..."
        
        # Search for docker-compose files with netbird services
        echo "Searching for docker-compose.yml files with Netbird services..."
        find / -name "docker-compose.yml" -type f 2>/dev/null | head -10 | while read -r file; do
            if grep -q "netbird\|management\|signal" "$file" 2>/dev/null; then
                echo "  Found potential installation: $(dirname "$file")"
            fi
        done
        
        # Check for running containers
        echo ""
        echo "Checking for running Netbird containers..."
        docker ps --format "{{.Names}}" | grep -E "management|signal|dashboard|coturn|relay|zitadel|caddy" || echo "No Netbird containers running"
    fi
    
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to test database connection
test_netbird_database() {
    echo "Testing Netbird Database Connection"
    echo "=================================="
    
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    cd "$netbird_path"
    
    # Find database container
    local db_container=""
    if docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^zdb$"; then
        db_container="zdb"
    elif docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^postgres$"; then
        db_container="postgres"
    fi
    
    if [ -n "$db_container" ]; then
        echo "Found database container: $db_container"
        
        # Test connection
        echo "Testing database connection..."
        if docker compose exec -T "$db_container" psql -U postgres -c "SELECT version();" 2>/dev/null; then
            echo -e "${GREEN}✓ Database connection successful${NC}"
            
            # List databases
            echo ""
            echo "Available databases:"
            docker compose exec -T "$db_container" psql -U postgres -c "\l" 2>/dev/null || echo "Could not list databases"
        else
            echo -e "${RED}✗ Database connection failed${NC}"
        fi
    else
        echo "No database container found."
    fi
    
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to test container detection
test_is_netbird_running() {
    echo "Testing Netbird Container Detection"
    echo "=================================="
    
    echo "Checking individual containers:"
    local containers=("management" "signal" "dashboard" "coturn" "relay" "zitadel" "zdb" "caddy")
    local running_count=0
    
    for container in "${containers[@]}"; do
        echo -n "  $container: "
        if docker ps --format "{{.Names}}" | grep -q "${container}"; then
            echo -e "${GREEN}Running${NC}"
            ((running_count++))
        else
            echo -e "${RED}Not running${NC}"
        fi
    done
    
    echo ""
    echo "Running containers: $running_count/${#containers[@]}"
    
    echo -n "Overall status: "
    if is_netbird_running; then
        echo -e "${GREEN}Netbird is considered running${NC}"
    else
        echo -e "${RED}Netbird is not running${NC}"
    fi
    
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to test domain detection
test_domain_detection() {
    echo "Testing Domain Detection"
    echo "======================="
    
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    echo "Installation path: $netbird_path"
    echo ""
    
    # Test domain detection
    local domain=$(detect_netbird_domain)
    if [ -n "$domain" ]; then
        echo -e "${GREEN}✓ Domain detected: $domain${NC}"
    else
        echo -e "${RED}✗ Domain not detected${NC}"
    fi
    
    echo ""
    echo "Checking configuration files for domain information:"
    
    cd "$netbird_path"
    
    # Check various files
    for file in zitadel.env setup.env docker-compose.yml .env; do
        if [ -f "$file" ]; then
            echo "  $file:"
            grep -E "DOMAIN|EXTERNAL" "$file" 2>/dev/null | head -3 | sed 's/^/    /' || echo "    No domain info found"
        fi
    done
    
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to install Docker if not present
install_docker() {
    if command -v docker >/dev/null 2>&1 && command -v docker-compose >/dev/null 2>&1; then
        return 0
    fi

    echo "Installing Docker and Docker Compose..."

    # Detect OS
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        sudo apt-get update
        sudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
        sudo apt-get update
        sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    elif [ -f /etc/redhat-release ]; then
        # RHEL/CentOS/Fedora
        sudo yum install -y yum-utils
        sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
        sudo systemctl start docker
        sudo systemctl enable docker
    else
        echo "Unsupported OS. Please install Docker manually."
        return 1
    fi

    # Add user to docker group
    sudo usermod -aG docker "$USER"

    # Start Docker service
    sudo systemctl start docker
    sudo systemctl enable docker

    echo "Docker installation completed."
    return 0
}

# Function to check compression tools
check_compression_tools() {
    local missing_tools=()

    if ! command -v tar >/dev/null 2>&1; then
        missing_tools+=("tar")
    fi

    if ! command -v gzip >/dev/null 2>&1; then
        missing_tools+=("gzip")
    fi

    if [ ${#missing_tools[@]} -gt 0 ]; then
        echo "Installing missing compression tools: ${missing_tools[*]}"

        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y "${missing_tools[@]}"
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y "${missing_tools[@]}"
        else
            echo "Cannot install missing tools automatically. Please install: ${missing_tools[*]}"
            return 1
        fi
    fi

    return 0
}

# Function to install rclone
install_rclone() {
    if command -v rclone >/dev/null 2>&1; then
        return 0
    fi

    echo "Installing rclone..."

    # Download and install rclone
    curl https://rclone.org/install.sh | sudo bash

    if command -v rclone >/dev/null 2>&1; then
        echo "Rclone installed successfully."
        return 0
    else
        echo "Failed to install rclone."
        return 1
    fi
}
