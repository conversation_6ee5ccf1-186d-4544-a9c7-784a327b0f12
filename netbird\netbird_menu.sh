#!/bin/bash

# Netbird menu functions
# This file contains the menu interfaces and UI functions

# Get the directory where this script is located
NETBIRD_MENU_DIR="$(dirname "${BASH_SOURCE[0]}")"

# Source required modules for menu functionality
source "$NETBIRD_MENU_DIR/netbird_core.sh"
source "$NETBIRD_MENU_DIR/netbird_install.sh"
source "$NETBIRD_MENU_DIR/netbird_utils.sh"
source "$NETBIRD_MENU_DIR/netbird_backup_config.sh"
source "$NETBIRD_MENU_DIR/netbird_backup_data.sh"
source "$NETBIRD_MENU_DIR/netbird_restore.sh"
source "$NETBIRD_MENU_DIR/netbird_menu_utils.sh"

# Main Netbird menu function
netbird_menu() {
    while true; do
        local status=$(get_installation_status)
        echo ""
        echo "Netbird Status: $status"
        echo ""

        options=(
            "01. Install Netbird"
            "02. Remove Netbird"
            "03. Check Netbird Status"
            "04. Start Netbird Services"
            "05. Stop Netbird Services"
            "06. Restart Netbird Services"
            "07. View Netbird Logs"
            "08. Container Information"
            "09. Check Connectivity"
            "10. Health Check"
            "11. Installation Info"
            "12. Backup Menu"
            "13. Restore Menu"
            "14. Update Netbird"
            "15. Debug Installation"
            "16. Test Database Connection"
            "17. Test Container Detection"
            "18. Test Domain Detection"
            "19. Back to Main Menu"
        )

        create_menu "Netbird Management Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                echo "Starting Netbird installation..."
                if install_netbird; then
                    echo -e "${GREEN}Netbird installation completed successfully.${NC}"
                else
                    echo -e "${RED}Netbird installation failed.${NC}"
                fi
                ;;
            2)
                echo "Starting Netbird removal..."
                if remove_netbird; then
                    echo -e "${GREEN}Netbird removal completed successfully.${NC}"
                else
                    echo -e "${RED}Netbird removal failed.${NC}"
                fi
                ;;
            3) check_netbird_status ;;
            4) start_netbird_services ;;
            5) stop_netbird_services ;;
            6) restart_netbird_services ;;
            7) view_netbird_logs ;;
            8) get_netbird_container_info ;;
            9) check_netbird_connectivity ;;
            10) netbird_health_check ;;
            11) show_netbird_info ;;
            12) netbird_backup_menu ;;
            13) netbird_restore_menu ;;
            14) update_netbird ;;
            15) debug_netbird_installation ;;
            16) test_netbird_database ;;
            17) test_is_netbird_running ;;
            18) test_domain_detection ;;
            19) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

# Netbird backup menu
netbird_backup_menu() {
    while true; do
        # Show current domain if detected
        local current_domain=$(detect_netbird_domain)
        local domain_info=""
        if [ -n "$current_domain" ]; then
            domain_info=" (Current domain: $current_domain)"
        else
            domain_info=" (Domain: not detected - will use 'unknown-domain')"
        fi

        echo ""
        echo "Netbird Backup Menu$domain_info"
        echo ""

        options=(
            "01. Test Backup Components"
            "02. Backup Configuration Only"
            "03. Backup Data Only"
            "04. Backup Docker Volumes Only"
            "05. COMPLETE Backup (Config + Data + Volumes)"
            "06. View Backup Status"
            "07. Back to Netbird Menu"
        )

        create_menu "Netbird Backup Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                echo "Testing backup components..."
                test_backup_components
                ;;
            2)
                echo "Starting configuration backup..."
                if backup_netbird_config; then
                    echo -e "${GREEN}Configuration backup completed successfully.${NC}"
                else
                    echo -e "${RED}Configuration backup failed.${NC}"
                fi
                ;;
            3)
                echo "Starting data backup..."
                if backup_netbird_data; then
                    echo -e "${GREEN}Data backup completed successfully.${NC}"
                else
                    echo -e "${RED}Data backup failed.${NC}"
                fi
                ;;
            4)
                echo "Starting volumes backup..."
                if backup_netbird_volumes; then
                    echo -e "${GREEN}Volumes backup completed successfully.${NC}"
                else
                    echo -e "${RED}Volumes backup failed.${NC}"
                fi
                ;;
            5)
                echo "Starting COMPLETE backup..."
                echo "This will backup everything needed for full restoration."
                if backup_netbird_full; then
                    echo -e "${GREEN}COMPLETE backup completed successfully.${NC}"
                else
                    echo -e "${RED}COMPLETE backup failed.${NC}"
                fi
                ;;
            6) view_backup_status ;;
            7) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

# Netbird restore menu
netbird_restore_menu() {
    while true; do
        options=(
            "01. Restore Configuration Only (Legacy backups)"
            "02. Restore Data Only (Legacy backups)"
            "03. Restore Docker Volumes Only (Legacy backups)"
            "04. COMPLETE Restore (Legacy backups)"
            "05. List Available Backups (All types)"
            "06. ⭐ RESTORE FROM DOMAIN (Recommended - Select from available domains)"
            "07. List Domain Backups"
            "08. Back to Netbird Menu"
        )

        create_menu "Netbird Restore Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                echo "Starting configuration restore (Legacy backup)..."
                echo "Note: This option is for legacy backups. For domain-based backups, use option 6."
                echo ""
                if restore_netbird_config; then
                    echo -e "${GREEN}Configuration restore completed successfully.${NC}"
                else
                    echo -e "${RED}Configuration restore failed.${NC}"
                    echo -e "${YELLOW}Tip: If you have domain-based backups, try option 6 instead.${NC}"
                fi
                ;;
            2)
                echo "Starting data restore (Legacy backup)..."
                echo "Note: This option is for legacy backups. For domain-based backups, use option 6."
                echo ""
                if restore_netbird_data; then
                    echo -e "${GREEN}Data restore completed successfully.${NC}"
                else
                    echo -e "${RED}Data restore failed.${NC}"
                    echo -e "${YELLOW}Tip: If you have domain-based backups, try option 6 instead.${NC}"
                fi
                ;;
            3)
                echo "Starting volumes restore (Legacy backup)..."
                echo "Note: This option is for legacy backups. For domain-based backups, use option 6."
                echo ""
                if restore_netbird_volumes; then
                    echo -e "${GREEN}Volumes restore completed successfully.${NC}"
                else
                    echo -e "${RED}Volumes restore failed.${NC}"
                    echo -e "${YELLOW}Tip: If you have domain-based backups, try option 6 instead.${NC}"
                fi
                ;;
            4)
                echo "Starting COMPLETE restore (Legacy backup)..."
                echo "Note: This option is for legacy backups. For domain-based backups, use option 6."
                echo "This will restore everything to a fully working state."
                echo ""
                if restore_netbird_full; then
                    echo -e "${GREEN}COMPLETE restore completed successfully.${NC}"
                else
                    echo -e "${RED}COMPLETE restore failed.${NC}"
                    echo -e "${YELLOW}Tip: If you have domain-based backups, try option 6 instead.${NC}"
                fi
                ;;
            5) list_netbird_backups ;;
            6)
                echo "Starting domain-based restore..."
                echo "This will show available domain backups and allow you to select one for complete restoration."
                if restore_netbird_from_domain; then
                    echo -e "${GREEN}Domain-based restore completed successfully.${NC}"
                else
                    echo -e "${RED}Domain-based restore failed.${NC}"
                fi
                ;;
            7) list_domain_backups ;;
            8) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

# Function to view backup status
view_backup_status() {
    echo "Netbird Backup Status"
    echo "===================="

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot check backup status."
        return 1
    fi

    echo "Checking cloud storage for backups..."

    # Check for domain-based backups first
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -n "$domains" ]; then
        echo ""
        echo "Domain-based backups found:"
        echo "============================"

        while IFS= read -r domain; do
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "---------------"
                local domain_path="$rclone_source/$domain"
                local files=$(rclone ls "$domain_path" 2>/dev/null)

                if [ -n "$files" ]; then
                    echo "$files" | while read -r size file; do
                        local size_human=$(numfmt --to=iec $size 2>/dev/null || echo "$size bytes")
                        echo "  - $file ($size_human)"
                    done
                else
                    echo "  - No files found"
                fi
            fi
        done <<< "$domains"
    fi

    # Check for legacy backups (files directly in Netbird folder)
    local legacy_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird.*backup")
    if [ -n "$legacy_files" ]; then
        echo ""
        echo "Legacy backups (not organized by domain):"
        echo "=========================================="
        echo "$legacy_files" | while read -r size file; do
            local size_human=$(numfmt --to=iec $size 2>/dev/null || echo "$size bytes")
            echo "  - $file ($size_human)"
        done
    fi

    if [ -z "$domains" ] && [ -z "$legacy_files" ]; then
        echo "No Netbird backups found in cloud storage."
    fi
    
    # Check local backup directory
    local backup_dir="/opt/netbird_backups"
    if [ -d "$backup_dir" ] && [ "$(ls -A "$backup_dir" 2>/dev/null)" ]; then
        echo ""
        echo "Local backup files:"
        echo "------------------"
        ls -la "$backup_dir"
    else
        echo ""
        echo "No local backup files found."
    fi
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to list available Netbird backups
list_netbird_backups() {
    echo "Available Netbird Backups"
    echo "========================="

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot list backups."
        return 1
    fi

    echo "Listing backups from cloud storage..."
    echo ""

    # Check for domain-based backups
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -n "$domains" ]; then
        echo "Domain-based backups:"
        echo "===================="

        while IFS= read -r domain; do
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "$(printf '%.0s-' $(seq 1 ${#domain}))"
                local domain_path="$rclone_source/$domain"

                if rclone lsl "$domain_path" 2>/dev/null; then
                    echo ""
                else
                    echo "  No files found or inaccessible"
                fi
            fi
        done <<< "$domains"

        echo ""
        echo "To restore from a specific domain, use option 6 in the restore menu."
    fi

    # Check for legacy backups
    local legacy_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird.*backup")
    if [ -n "$legacy_files" ]; then
        echo ""
        echo "Legacy backups (not organized by domain):"
        echo "=========================================="
        rclone lsl "$rclone_source" 2>/dev/null | grep "netbird.*backup"
        echo ""
        echo "Legacy backups can be restored using options 1-4 in the restore menu."
    fi

    if [ -z "$domains" ] && [ -z "$legacy_files" ]; then
        echo "No backups found or unable to access cloud storage."
        echo "Please check your rclone configuration."
    fi

    echo ""
    read -rp "Press Enter to continue..."
}

# Function to show Netbird installation info
show_netbird_info() {
    echo "Netbird Installation Information"
    echo "==============================="

    local netbird_path=$(detect_netbird_installation)

    if [ -n "$netbird_path" ]; then
        echo "✓ Installation found at: $netbird_path"
        echo ""

        # Show configuration files
        echo "Configuration files:"
        cd "$netbird_path"
        for file in docker-compose.yml management.json turnserver.conf Caddyfile zitadel.env dashboard.env; do
            if [ -f "$file" ]; then
                echo "  ✓ $file"
            else
                echo "  ✗ $file (missing)"
            fi
        done

        echo ""
        echo "Docker containers:"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep -E "management|signal|dashboard|coturn|relay|zitadel|caddy" || echo "No Netbird containers found"

        # Show credentials if available
        if [ -f "$netbird_path/.env" ]; then
            echo ""
            echo "Login credentials:"
            cat "$netbird_path/.env"
        fi

        # Show domain info
        if [ -f "$netbird_path/zitadel.env" ]; then
            local domain=$(grep "ZITADEL_EXTERNALDOMAIN" "$netbird_path/zitadel.env" 2>/dev/null | cut -d'=' -f2)
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "Access URL: https://$domain"
            fi
        fi

    else
        echo "✗ Netbird installation not detected."
        echo ""
        echo "To install Netbird:"
        echo "  1. Select 'Install Netbird' from the menu"
        echo "  2. Provide your domain name"
        echo "  3. Wait for automatic installation"
        echo ""
        echo "Default installation location: /opt/netbird"
    fi
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to perform Netbird health check
netbird_health_check() {
    echo "Netbird Health Check"
    echo "==================="
    
    local health_score=0
    local max_score=10
    
    # Check if installation is detected
    echo -n "Installation detection: "
    if detect_netbird_installation >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Found${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Not found${NC}"
    fi
    
    # Check if services are running
    echo -n "Services running: "
    if is_netbird_running; then
        echo -e "${GREEN}✓ Running${NC}"
        ((health_score += 3))
    else
        echo -e "${RED}✗ Not running${NC}"
    fi
    
    # Check connectivity
    echo -n "Management API: "
    if curl -s -k https://localhost:443/api/status >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Accessible${NC}"
        ((health_score += 2))
    else
        echo -e "${RED}✗ Not accessible${NC}"
    fi
    
    echo -n "Signal service: "
    if netstat -tuln | grep -q ":33073"; then
        echo -e "${GREEN}✓ Port open${NC}"
        ((health_score += 2))
    else
        echo -e "${RED}✗ Port not open${NC}"
    fi
    
    echo -n "Dashboard: "
    if curl -s http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Accessible${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Not accessible${NC}"
    fi
    
    echo -n "TURN server: "
    if netstat -tuln | grep -q ":3478"; then
        echo -e "${GREEN}✓ Port open${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Port not open${NC}"
    fi
    
    echo ""
    echo "Health Score: $health_score/$max_score"
    
    if [ $health_score -ge 8 ]; then
        echo -e "${GREEN}Status: Excellent${NC}"
    elif [ $health_score -ge 6 ]; then
        echo -e "${YELLOW}Status: Good${NC}"
    elif [ $health_score -ge 4 ]; then
        echo -e "${YELLOW}Status: Fair${NC}"
    else
        echo -e "${RED}Status: Poor${NC}"
    fi
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to view backup status
view_backup_status() {
    echo "Netbird Backup Status"
    echo "===================="

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot check backup status."
        return 1
    fi

    echo "Checking cloud storage for backups..."

    # Check for domain-based backups first
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -n "$domains" ]; then
        echo ""
        echo "Domain-based backups found:"
        echo "============================"

        while IFS= read -r domain; do
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "---------------"
                local domain_path="$rclone_source/$domain"
                local files=$(rclone ls "$domain_path" 2>/dev/null)

                if [ -n "$files" ]; then
                    echo "$files" | while read -r size file; do
                        local size_human=$(numfmt --to=iec $size 2>/dev/null || echo "$size bytes")
                        echo "  - $file ($size_human)"
                    done
                else
                    echo "  - No files found"
                fi
            fi
        done <<< "$domains"
    fi

    # Check for legacy backups (files directly in Netbird folder)
    local legacy_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird.*backup")
    if [ -n "$legacy_files" ]; then
        echo ""
        echo "Legacy backups (not organized by domain):"
        echo "=========================================="
        echo "$legacy_files" | while read -r size file; do
            local size_human=$(numfmt --to=iec $size 2>/dev/null || echo "$size bytes")
            echo "  - $file ($size_human)"
        done
    fi

    if [ -z "$domains" ] && [ -z "$legacy_files" ]; then
        echo "No Netbird backups found in cloud storage."
    fi

    # Check local backup directory
    local backup_dir="/opt/netbird_backups"
    if [ -d "$backup_dir" ] && [ "$(ls -A "$backup_dir" 2>/dev/null)" ]; then
        echo ""
        echo "Local backup files:"
        echo "------------------"
        ls -la "$backup_dir"
    else
        echo ""
        echo "No local backup files found."
    fi
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to show Netbird installation info
show_netbird_info() {
    echo "Netbird Installation Information"
    echo "==============================="

    local netbird_path=$(detect_netbird_installation)

    if [ -n "$netbird_path" ]; then
        echo "✓ Installation found at: $netbird_path"
        echo ""

        # Show configuration files
        echo "Configuration files:"
        cd "$netbird_path"
        for file in docker-compose.yml management.json turnserver.conf Caddyfile zitadel.env dashboard.env; do
            if [ -f "$file" ]; then
                echo "  ✓ $file"
            else
                echo "  ✗ $file (missing)"
            fi
        done

        echo ""
        echo "Docker containers:"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep -E "management|signal|dashboard|coturn|relay|zitadel|caddy" || echo "No Netbird containers found"

        # Show credentials if available
        if [ -f "$netbird_path/.env" ]; then
            echo ""
            echo "Login credentials:"
            cat "$netbird_path/.env"
        fi

        # Show domain info
        if [ -f "$netbird_path/zitadel.env" ]; then
            local domain=$(grep "ZITADEL_EXTERNALDOMAIN" "$netbird_path/zitadel.env" 2>/dev/null | cut -d'=' -f2)
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "Access URL: https://$domain"
            fi
        fi

    else
        echo "✗ Netbird installation not detected."
        echo ""
        echo "To install Netbird:"
        echo "  1. Select 'Install Netbird' from the menu"
        echo "  2. Provide your domain name"
        echo "  3. Wait for automatic installation"
        echo ""
        echo "Default installation location: /opt/netbird"
    fi
    echo ""
    read -rp "Press Enter to continue..."
}
