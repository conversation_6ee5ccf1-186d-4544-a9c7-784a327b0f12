#!/bin/bash

# Function to backup Netbird configuration files
backup_netbird_config() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    # Detect domain name for organized backup
    local domain=$(detect_netbird_domain)
    if [ -z "$domain" ]; then
        echo "Warning: Could not detect domain name. Using 'unknown-domain' as fallback."
        domain="unknown-domain"
    fi

    echo "Detected domain: $domain"

    local backup_dir="/opt/netbird_backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local config_backup_file="netbird_config_backup_${timestamp}.tar.gz"
    local config_backup_path="$backup_dir/$config_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird/$domain"
    
    echo "Backing up Netbird configuration files..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi
    
    # Create list of config files to backup
    local config_files=()
    cd "$netbird_path"
    
    # Common configuration files
    [ -f "docker-compose.yml" ] && config_files+=("docker-compose.yml")
    [ -f "management.json" ] && config_files+=("management.json")
    [ -f "turnserver.conf" ] && config_files+=("turnserver.conf")
    [ -f "setup.env" ] && config_files+=("setup.env")
    [ -f "Caddyfile" ] && config_files+=("Caddyfile")
    [ -f "zitadel.env" ] && config_files+=("zitadel.env")
    [ -f "dashboard.env" ] && config_files+=("dashboard.env")
    [ -f "relay.env" ] && config_files+=("relay.env")
    [ -f "zdb.env" ] && config_files+=("zdb.env")
    
    if [ ${#config_files[@]} -eq 0 ]; then
        echo "No configuration files found to backup."
        return 1
    fi
    
    # Create tar archive of config files
    echo "Creating configuration backup archive..."
    if tar -czf "$config_backup_path" "${config_files[@]}" 2>/dev/null; then
        echo "Configuration backup created successfully."
        
        # Upload to cloud storage (overwrite existing backup for this domain)
        echo "Uploading configuration backup to cloud storage..."
        echo "Destination: $rclone_dest"

        # Remove existing config backup for this domain first
        echo "Removing any existing configuration backup for domain: $domain"
        rclone delete "$rclone_dest" --include "netbird_config_backup_*.tar.gz" 2>/dev/null || true

        if rclone copy "$config_backup_path" "$rclone_dest" --progress; then
            echo "Configuration backup uploaded successfully to domain folder: $domain"
            rm -f "$config_backup_path"
            return 0
        else
            echo "Failed to upload configuration backup."
            rm -f "$config_backup_path"
            return 1
        fi
    else
        echo "Failed to create configuration backup."
        return 1
    fi
}

# Function to backup Docker volumes
backup_netbird_volumes() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    # Detect domain name for organized backup
    local domain=$(detect_netbird_domain)
    if [ -z "$domain" ]; then
        echo "Warning: Could not detect domain name. Using 'unknown-domain' as fallback."
        domain="unknown-domain"
    fi

    echo "Detected domain: $domain"

    local backup_dir="/opt/netbird_backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local volumes_backup_file="netbird_volumes_backup_${timestamp}.tar.gz"
    local volumes_backup_path="$backup_dir/$volumes_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Backing up Netbird Docker volumes..."

    # Create backup directory
    mkdir -p "$backup_dir"

    cd "$netbird_path"

    # Get list of volumes used by Netbird - improved detection
    local volumes=""
    local temp_volumes_dir="$backup_dir/volumes_temp"
    mkdir -p "$temp_volumes_dir"

    # Stop all services for consistent backup
    echo "Stopping all Netbird services for consistent volume backup..."
    docker compose stop

    # Get volumes from docker compose config
    volumes=$(docker compose config --volumes 2>/dev/null | tr '\n' ' ')

    # Also get volumes from running containers (with netbird prefix)
    local netbird_volumes=$(docker volume ls --format "{{.Name}}" | grep -E "netbird.*netbird" | tr '\n' ' ')
    volumes="$volumes $netbird_volumes"

    # Remove duplicates and empty entries, prioritize prefixed volumes
    volumes=$(echo "$volumes" | tr ' ' '\n' | sort -u | grep -v '^$')

    # Filter out non-prefixed duplicates (prefer netbird_netbird_* over netbird_*)
    local filtered_volumes=""
    for vol in $volumes; do
        if [[ "$vol" =~ ^netbird_[^_]+$ ]]; then
            # Check if prefixed version exists
            local prefixed_vol="netbird_$vol"
            if echo "$volumes" | grep -q "^$prefixed_vol$"; then
                continue  # Skip non-prefixed if prefixed exists
            fi
        fi
        filtered_volumes="$filtered_volumes $vol"
    done

    volumes=$(echo "$filtered_volumes" | tr ' ' '\n' | sort -u | grep -v '^$' | tr '\n' ' ')

    echo "Found volumes to backup: $volumes"

    # Backup each volume
    echo "Backing up Docker volumes..."
    local volumes_backed_up=0

    for volume in $volumes; do
        volume=$(echo "$volume" | tr -d ' ')  # Remove any whitespace
        if [ -n "$volume" ] && docker volume inspect "$volume" >/dev/null 2>&1; then
            echo "Backing up volume: $volume"
            local volume_backup_dir="$temp_volumes_dir/$volume"
            mkdir -p "$volume_backup_dir"

            # Create a temporary container to access the volume
            if docker run --rm -v "$volume:/volume" -v "$volume_backup_dir:/backup" alpine:latest \
                sh -c "cd /volume && tar -czf /backup/volume_data.tar.gz . 2>/dev/null" 2>/dev/null; then
                echo "Successfully backed up volume: $volume"
                ((volumes_backed_up++))
            else
                echo "Warning: Failed to backup volume $volume"
            fi
        else
            echo "Volume $volume not found or inaccessible"
        fi
    done

    echo "Total volumes backed up: $volumes_backed_up"

    # Create archive of all volume backups
    if [ "$(ls -A "$temp_volumes_dir" 2>/dev/null)" ]; then
        echo "Creating volumes backup archive..."
        cd "$backup_dir"
        if tar -czf "$volumes_backup_file" -C "$temp_volumes_dir" .; then
            echo "Volumes backup created successfully."
            rm -rf "$temp_volumes_dir"

            # Upload to cloud storage (overwrite existing backup for this domain)
            echo "Uploading volumes backup to cloud storage..."
            echo "Destination: $rclone_dest"

            # Remove existing volumes backup for this domain first
            echo "Removing any existing volumes backup for domain: $domain"
            rclone delete "$rclone_dest" --include "netbird_volumes_backup_*.tar.gz" 2>/dev/null || true

            if rclone copy "$volumes_backup_path" "$rclone_dest" --progress; then
                echo "Volumes backup uploaded successfully to domain folder: $domain"
                rm -f "$volumes_backup_path"

                # Restart services
                echo "Restarting Netbird services..."
                cd "$netbird_path"
                docker compose up -d
                return 0
            else
                echo "Failed to upload volumes backup."
                rm -f "$volumes_backup_path"
                cd "$netbird_path"
                docker compose up -d
                return 1
            fi
        else
            echo "Failed to create volumes backup archive."
            rm -rf "$temp_volumes_dir"
            cd "$netbird_path"
            docker compose up -d
            return 1
        fi
    else
        echo "No volumes found to backup."
        rm -rf "$temp_volumes_dir"
        cd "$netbird_path"
        docker compose up -d
        return 0
    fi
}

# Function to backup Netbird data (databases and persistent data)
backup_netbird_data() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    # Detect domain name for organized backup
    local domain=$(detect_netbird_domain)
    if [ -z "$domain" ]; then
        echo "Warning: Could not detect domain name. Using 'unknown-domain' as fallback."
        domain="unknown-domain"
    fi

    echo "Detected domain: $domain"

    local backup_dir="/opt/netbird_backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local data_backup_file="netbird_data_backup_${timestamp}.tar.gz"
    local data_backup_path="$backup_dir/$data_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Backing up Netbird application data..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi

    cd "$netbird_path"

    # Create comprehensive data backup
    local temp_data_dir="$backup_dir/netbird_data_temp"
    mkdir -p "$temp_data_dir"

    # Stop services for consistent backup
    echo "Stopping services for consistent data backup..."
    docker compose stop

    # Backup management data
    echo "Backing up management service data..."
    if docker compose cp -a management:/var/lib/netbird/ "$temp_data_dir/management_data/" 2>/dev/null; then
        echo "Management data backed up successfully."
    else
        echo "Warning: Could not backup management data (container may not exist yet)."
    fi

    # Backup Zitadel database - improved detection and backup
    echo "Backing up Zitadel database..."
    local db_container=""

    # Find database container - improved detection
    echo "Detecting database container..."

    # Method 1: Check running containers by service name
    if docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^zdb$"; then
        db_container="zdb"
        echo "Found database service: zdb"
    elif docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^postgres$"; then
        db_container="postgres"
        echo "Found database service: postgres"
    else
        # Method 2: Check all services in compose file
        if docker compose config --services 2>/dev/null | grep -q "^zdb$"; then
            db_container="zdb"
            echo "Found database service in config: zdb"
        elif docker compose config --services 2>/dev/null | grep -q "^postgres$"; then
            db_container="postgres"
            echo "Found database service in config: postgres"
        else
            # Method 3: Check by container name pattern
            if docker ps --format "{{.Names}}" | grep -q "zdb"; then
                db_container="zdb"
                echo "Found database container by name pattern: zdb"
            elif docker ps --format "{{.Names}}" | grep -q "postgres"; then
                db_container="postgres"
                echo "Found database container by name pattern: postgres"
            else
                echo "No database service found in compose configuration or running containers"
            fi
        fi
    fi

    if [ -n "$db_container" ]; then
        mkdir -p "$temp_data_dir/zitadel_db"
        echo "Found database container: $db_container"

        # Start only the database container for backup
        echo "Starting database container for backup..."
        docker compose up -d "$db_container"

        # Wait for database to be ready
        echo "Waiting for database to be ready..."
        sleep 15

        # Try multiple backup methods
        local backup_success=false

        # Method 1: pg_dumpall
        echo "Attempting database backup with pg_dumpall..."
        if docker compose exec -T "$db_container" pg_dumpall -U postgres > "$temp_data_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null; then
            # Verify backup file is not empty
            if [ -s "$temp_data_dir/zitadel_db/zitadel_backup.sql" ]; then
                local backup_size=$(stat -c%s "$temp_data_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null || echo "0")
                echo "Zitadel database backed up successfully with pg_dumpall ($backup_size bytes)."
                backup_success=true
            else
                echo "pg_dumpall produced empty backup file."
            fi
        else
            echo "pg_dumpall failed, trying alternative method..."

            # Method 2: pg_dump for specific databases
            echo "Attempting backup of individual databases..."
            if docker compose exec -T "$db_container" psql -U postgres -c "\l" 2>/dev/null | grep -q "zitadel"; then
                if docker compose exec -T "$db_container" pg_dump -U postgres zitadel > "$temp_data_dir/zitadel_db/zitadel_db_backup.sql" 2>/dev/null; then
                    echo "Zitadel database backed up successfully with pg_dump."
                    backup_success=true
                fi
            fi
        fi

        if ! $backup_success; then
            echo "Warning: Could not backup Zitadel database using SQL dump methods."
            echo "Attempting to backup database data directory..."

            # Method 3: Backup data directory
            if docker compose exec -T "$db_container" tar -czf /tmp/postgres_data.tar.gz /var/lib/postgresql/data 2>/dev/null; then
                docker compose cp "$db_container:/tmp/postgres_data.tar.gz" "$temp_data_dir/zitadel_db/" 2>/dev/null && {
                    echo "Database data directory backed up successfully."
                    backup_success=true
                }
            fi
        fi

        if ! $backup_success; then
            echo "Warning: All database backup methods failed."
        fi

        docker compose stop "$db_container"
    else
        echo "Warning: No database container found for backup."
    fi

    # Backup machine keys and certificates
    echo "Backing up certificates and keys..."
    if [ -d "machinekey" ]; then
        cp -r machinekey "$temp_data_dir/" 2>/dev/null || echo "Warning: Could not backup machine keys."
    fi

    # Backup any additional data directories
    for dir in "certs" "ssl" "data" "logs"; do
        if [ -d "$dir" ]; then
            cp -r "$dir" "$temp_data_dir/" 2>/dev/null
        fi
    done

    # Create tar archive of all data
    echo "Creating comprehensive data backup archive..."
    cd "$backup_dir"
    if tar -czf "$data_backup_file" -C "$temp_data_dir" .; then
        echo "Data backup created successfully."
        rm -rf "$temp_data_dir"

        # Upload to cloud storage (overwrite existing backup for this domain)
        echo "Uploading data backup to cloud storage..."
        echo "Destination: $rclone_dest"

        # Remove existing data backup for this domain first
        echo "Removing any existing data backup for domain: $domain"
        rclone delete "$rclone_dest" --include "netbird_data_backup_*.tar.gz" 2>/dev/null || true

        if rclone copy "$data_backup_path" "$rclone_dest" --progress; then
            echo "Data backup uploaded successfully to domain folder: $domain"
            rm -f "$data_backup_path"

            # Restart all services
            echo "Restarting Netbird services..."
            cd "$netbird_path"
            docker compose up -d
            return 0
        else
            echo "Failed to upload data backup."
            rm -f "$data_backup_path"
            cd "$netbird_path"
            docker compose up -d
            return 1
        fi
    else
        echo "Failed to create data backup archive."
        rm -rf "$temp_data_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to perform full Netbird backup (config + data + volumes)
backup_netbird_full() {
    echo "Starting COMPLETE Netbird backup..."
    echo "==================================="
    echo "This will backup:"
    echo "- Configuration files (docker-compose.yml, etc.)"
    echo "- Application data (management database, etc.)"
    echo "- Docker volumes (Zitadel DB, Caddy data, certificates)"
    echo "- Machine keys and certificates"
    echo ""

    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    local success=true
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")

    echo "Backup timestamp: $backup_timestamp"
    echo ""

    # Backup configuration
    echo "Step 1/3: Backing up configuration files..."
    if ! backup_netbird_config; then
        echo "Configuration backup failed."
        success=false
    fi
    echo ""

    # Backup application data
    echo "Step 2/3: Backing up application data and databases..."
    if ! backup_netbird_data; then
        echo "Data backup failed."
        success=false
    fi
    echo ""

    # Backup Docker volumes
    echo "Step 3/3: Backing up Docker volumes..."
    if ! backup_netbird_volumes; then
        echo "Volumes backup failed."
        success=false
    fi
    echo ""

    if $success; then
        echo "========================================="
        echo "COMPLETE Netbird backup finished successfully!"
        echo "========================================="
        echo "Backup includes:"
        echo "✓ Configuration files"
        echo "✓ Management database"
        echo "✓ Zitadel identity provider database"
        echo "✓ Docker volumes (Caddy, certificates, etc.)"
        echo "✓ Machine keys and certificates"
        echo ""
        echo "This backup can be used to restore a fully working"
        echo "Netbird installation on any compatible system."
        return 0
    else
        echo "========================================="
        echo "Netbird backup completed with ERRORS!"
        echo "========================================="
        echo "Some components may not have been backed up properly."
        echo "Check the output above for details."
        return 1
    fi
}

# Function to restore Netbird configuration
restore_netbird_config() {
    local backup_dir="/opt/netbird_backups"
    local temp_config_backup=""
    local rclone_source=""

    # Ask user to select domain backup
    echo "Available domain backups for configuration restore:"
    echo "=================================================="

    if ! list_domain_backups; then
        echo "No domain backups available. Trying legacy backup..."
        # Fallback to legacy backup
        local config_backup_file="netbird_config_backup.tar.gz"
        temp_config_backup="$backup_dir/$config_backup_file"
        rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    else
        echo ""
        read -rp "Enter the domain name to restore from: " selected_domain

        if [ -z "$selected_domain" ]; then
            echo "No domain selected. Restore cancelled."
            return 1
        fi

        rclone_source="$RCLONE_REMOTE:/Backups/Netbird/$selected_domain"

        # Find the latest config backup file for this domain
        local config_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_config_backup_.*\.tar\.gz" | sort -k2 -r)

        if [ -z "$config_files" ]; then
            echo "No configuration backup found for domain: $selected_domain"
            return 1
        fi

        local latest_config_file=$(echo "$config_files" | head -1 | awk '{print $2}')
        temp_config_backup="$backup_dir/$latest_config_file"

        echo "Selected backup file: $latest_config_file"
    fi
    
    echo "Restoring Netbird configuration..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Download configuration backup
    echo "Downloading configuration backup from cloud storage..."
    rm -f "$temp_config_backup"

    local backup_filename=$(basename "$temp_config_backup")
    if ! rclone copy "$rclone_source/$backup_filename" "$backup_dir" --progress; then
        echo "Failed to download configuration backup."
        return 1
    fi
    
    if [ ! -f "$temp_config_backup" ]; then
        echo "Configuration backup file not found after download."
        return 1
    fi
    
    # Ask for installation directory
    echo "Where would you like to restore the Netbird configuration?"
    read -rp "Enter path (default: /opt/netbird): " restore_path
    restore_path=${restore_path:-/opt/netbird}
    
    # Create restore directory
    mkdir -p "$restore_path"
    
    # Extract configuration files
    echo "Extracting configuration files..."
    cd "$restore_path"
    if tar -xzf "$temp_config_backup"; then
        echo "Configuration restored successfully to $restore_path"
        rm -f "$temp_config_backup"
        return 0
    else
        echo "Failed to extract configuration files."
        rm -f "$temp_config_backup"
        return 1
    fi
}

# Function to restore Docker volumes
restore_netbird_volumes() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local temp_volumes_backup=""
    local rclone_source=""

    # Ask user to select domain backup
    echo "Available domain backups for volumes restore:"
    echo "============================================="

    if ! list_domain_backups; then
        echo "No domain backups available. Trying legacy backup..."
        # Fallback to legacy backup
        local volumes_backup_file="netbird_volumes_backup.tar.gz"
        temp_volumes_backup="$backup_dir/$volumes_backup_file"
        rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    else
        echo ""
        read -rp "Enter the domain name to restore volumes from: " selected_domain

        if [ -z "$selected_domain" ]; then
            echo "No domain selected. Restore cancelled."
            return 1
        fi

        rclone_source="$RCLONE_REMOTE:/Backups/Netbird/$selected_domain"

        # Find the latest volumes backup file for this domain
        local volumes_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_volumes_backup_.*\.tar\.gz" | sort -k2 -r)

        if [ -z "$volumes_files" ]; then
            echo "No volumes backup found for domain: $selected_domain"
            echo "Skipping volume restoration..."
            return 0  # Return success since this is not critical
        fi

        local latest_volumes_file=$(echo "$volumes_files" | head -1 | awk '{print $2}')
        temp_volumes_backup="$backup_dir/$latest_volumes_file"

        echo "Selected backup file: $latest_volumes_file"
    fi

    echo "Restoring Netbird Docker volumes..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Download volumes backup
    echo "Downloading volumes backup from cloud storage..."
    rm -f "$temp_volumes_backup"

    local backup_filename=$(basename "$temp_volumes_backup")
    if ! rclone copy "$rclone_source/$backup_filename" "$backup_dir" --progress; then
        echo "Volumes backup not found in cloud storage."
        echo "This may be because no volumes were backed up originally."
        echo "Skipping volume restoration..."
        return 0  # Return success since this is not critical
    fi

    if [ ! -f "$temp_volumes_backup" ]; then
        echo "Volumes backup file not found after download."
        echo "Skipping volume restoration..."
        return 0  # Return success since this is not critical
    fi

    # Stop all services
    echo "Stopping all Netbird services..."
    cd "$netbird_path"
    docker compose down

    # Extract volumes backup
    echo "Extracting volumes backup..."
    local temp_volumes_dir="$backup_dir/volumes_restore_temp"
    mkdir -p "$temp_volumes_dir"

    cd "$temp_volumes_dir"
    if tar -xzf "$temp_volumes_backup"; then
        echo "Restoring Docker volumes..."

        # Restore each volume
        for volume_dir in */; do
            if [ -d "$volume_dir" ]; then
                local volume_name=$(basename "$volume_dir")
                echo "Restoring volume: $volume_name"

                # Create volume if it doesn't exist
                docker volume create "$volume_name" >/dev/null 2>&1

                # Restore volume data
                if [ -f "$volume_dir/volume_data.tar.gz" ]; then
                    docker run --rm -v "$volume_name:/volume" -v "$(pwd)/$volume_dir:/backup" alpine:latest \
                        sh -c "cd /volume && tar -xzf /backup/volume_data.tar.gz" 2>/dev/null || {
                        echo "Warning: Failed to restore volume $volume_name"
                    }
                fi
            fi
        done

        echo "Volumes restored successfully."
        rm -f "$temp_volumes_backup"
        rm -rf "$temp_volumes_dir"

        # Start services
        echo "Starting Netbird services..."
        cd "$netbird_path"
        docker compose up -d
        return 0
    else
        echo "Failed to extract volumes backup."
        rm -f "$temp_volumes_backup"
        rm -rf "$temp_volumes_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to restore Netbird data
restore_netbird_data() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local temp_data_backup=""
    local rclone_source=""

    # Ask user to select domain backup
    echo "Available domain backups for data restore:"
    echo "=========================================="

    if ! list_domain_backups; then
        echo "No domain backups available. Trying legacy backup..."
        # Fallback to legacy backup
        local data_backup_file="netbird_data_backup.tar.gz"
        temp_data_backup="$backup_dir/$data_backup_file"
        rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    else
        echo ""
        read -rp "Enter the domain name to restore data from: " selected_domain

        if [ -z "$selected_domain" ]; then
            echo "No domain selected. Restore cancelled."
            return 1
        fi

        rclone_source="$RCLONE_REMOTE:/Backups/Netbird/$selected_domain"

        # Find the latest data backup file for this domain
        local data_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_data_backup_.*\.tar\.gz" | sort -k2 -r)

        if [ -z "$data_files" ]; then
            echo "No data backup found for domain: $selected_domain"
            return 1
        fi

        local latest_data_file=$(echo "$data_files" | head -1 | awk '{print $2}')
        temp_data_backup="$backup_dir/$latest_data_file"

        echo "Selected backup file: $latest_data_file"
    fi

    echo "Restoring Netbird application data..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Download data backup
    echo "Downloading data backup from cloud storage..."
    rm -f "$temp_data_backup"

    local backup_filename=$(basename "$temp_data_backup")
    if ! rclone copy "$rclone_source/$backup_filename" "$backup_dir" --progress; then
        echo "Failed to download data backup."
        return 1
    fi

    if [ ! -f "$temp_data_backup" ]; then
        echo "Data backup file not found after download."
        return 1
    fi

    # Stop all services for consistent restore
    echo "Stopping all services for data restoration..."
    cd "$netbird_path"
    docker compose down

    # Extract and restore data
    echo "Extracting and restoring application data..."
    local temp_restore_dir="$backup_dir/data_restore_temp"
    mkdir -p "$temp_restore_dir"

    cd "$temp_restore_dir"
    if tar -xzf "$temp_data_backup"; then
        # Restore management data
        if [ -d "management_data" ]; then
            echo "Restoring management data..."
            # Start only management container to restore data
            cd "$netbird_path"
            docker compose up -d management
            sleep 10

            # Copy management data
            if docker compose cp -a "$temp_restore_dir/management_data/." management:/var/lib/netbird/; then
                echo "Management data restored successfully."
            else
                echo "Warning: Failed to restore management data."
            fi
            docker compose stop management
        fi

        # Restore Zitadel database
        if [ -d "zitadel_db" ]; then
            echo "Restoring Zitadel database..."
            cd "$netbird_path"

            # Start database container
            local db_container=$(docker compose config --services | grep -E "postgres|zdb" | head -1)
            if [ -n "$db_container" ]; then
                docker compose up -d "$db_container"
                echo "Waiting for database to be ready..."
                sleep 20

                local restore_success=false

                # Method 1: Try pg_dumpall restore
                if [ -f "$temp_restore_dir/zitadel_db/zitadel_backup.sql" ]; then
                    echo "Attempting to restore database with pg_dumpall backup..."
                    if docker compose exec -T "$db_container" psql -U postgres < "$temp_restore_dir/zitadel_db/zitadel_backup.sql" >/dev/null 2>&1; then
                        echo "Zitadel database restored successfully with pg_dumpall."
                        restore_success=true
                    fi
                fi

                # Method 2: Try individual database restore
                if ! $restore_success && [ -f "$temp_restore_dir/zitadel_db/zitadel_db_backup.sql" ]; then
                    echo "Attempting to restore individual database..."
                    # Create database if it doesn't exist
                    docker compose exec -T "$db_container" psql -U postgres -c "CREATE DATABASE zitadel;" 2>/dev/null
                    if docker compose exec -T "$db_container" psql -U postgres -d zitadel < "$temp_restore_dir/zitadel_db/zitadel_db_backup.sql" >/dev/null 2>&1; then
                        echo "Zitadel database restored successfully with pg_dump."
                        restore_success=true
                    fi
                fi

                # Method 3: Try data directory restore
                if ! $restore_success && [ -f "$temp_restore_dir/zitadel_db/postgres_data.tar.gz" ]; then
                    echo "Attempting to restore database data directory..."
                    docker compose stop "$db_container"
                    sleep 5

                    # Copy data directory backup to container
                    docker compose cp "$temp_restore_dir/zitadel_db/postgres_data.tar.gz" "$db_container:/tmp/" 2>/dev/null
                    docker compose start "$db_container"
                    sleep 10

                    if docker compose exec -T "$db_container" sh -c "cd / && tar -xzf /tmp/postgres_data.tar.gz" 2>/dev/null; then
                        echo "Database data directory restored successfully."
                        restore_success=true
                    fi
                fi

                if $restore_success; then
                    echo "Zitadel database restored successfully."
                else
                    echo "Warning: All database restore methods failed."
                fi

                docker compose stop "$db_container"
            else
                echo "Warning: No database container found for restore."
            fi
        else
            echo "No Zitadel database backup found to restore."
        fi

        # Restore machine keys and certificates
        if [ -d "machinekey" ]; then
            echo "Restoring machine keys and certificates..."
            cd "$netbird_path"
            cp -r "$temp_restore_dir/machinekey" . 2>/dev/null || echo "Warning: Failed to restore machine keys."
        fi

        # Restore additional data directories
        cd "$netbird_path"
        for dir in "certs" "ssl" "data" "logs"; do
            if [ -d "$temp_restore_dir/$dir" ]; then
                cp -r "$temp_restore_dir/$dir" . 2>/dev/null
            fi
        done

        echo "Application data restored successfully."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"

        # Start all services
        echo "Starting all Netbird services..."
        docker compose up -d
        return 0
    else
        echo "Failed to extract data backup."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to perform COMPLETE Netbird restore
restore_netbird_full() {
    echo "Starting COMPLETE Netbird restore..."
    echo "===================================="
    echo "This will restore:"
    echo "- Configuration files"
    echo "- Application data and databases"
    echo "- Docker volumes"
    echo "- Machine keys and certificates"
    echo ""
    echo "WARNING: This will overwrite any existing Netbird installation!"
    read -rp "Are you sure you want to continue? (y/n): " confirm_restore
    if [[ ! "$confirm_restore" =~ ^[Yy]$ ]]; then
        echo "Restore cancelled."
        return 1
    fi

    local success=true

    echo ""
    echo "Starting complete restoration process..."
    echo ""

    # Restore configuration first
    echo "Step 1/4: Restoring configuration files..."
    if ! restore_netbird_config; then
        echo "Configuration restore failed."
        success=false
    fi
    echo ""

    # Restore Docker volumes
    echo "Step 2/4: Restoring Docker volumes..."
    if ! restore_netbird_volumes; then
        echo "Volumes restore failed."
        success=false
    fi
    echo ""

    # Restore application data
    echo "Step 3/4: Restoring application data..."
    if ! restore_netbird_data; then
        echo "Data restore failed."
        success=false
    fi
    echo ""

    # Final service restart and verification
    echo "Step 4/4: Final service restart and verification..."
    local netbird_path=$(detect_netbird_installation)
    if [ -n "$netbird_path" ]; then
        cd "$netbird_path"
        echo "Restarting all services with restored data..."
        docker compose down
        sleep 5
        docker compose up -d

        # Wait for services to start
        echo "Waiting for services to start..."
        sleep 30

        # Check if services are running
        if is_netbird_running; then
            echo "Services started successfully."
        else
            echo "Warning: Some services may not have started properly."
            success=false
        fi
    fi
    echo ""

    if $success; then
        echo "================================================="
        echo "COMPLETE Netbird restore finished successfully!"
        echo "================================================="
        echo "Restored components:"
        echo "✓ Configuration files"
        echo "✓ Management database"
        echo "✓ Zitadel identity provider database"
        echo "✓ Docker volumes (Caddy, certificates, etc.)"
        echo "✓ Machine keys and certificates"
        echo ""
        echo "Your Netbird installation should now be fully"
        echo "functional and identical to the backed up state."
        echo ""

        # Show access information
        if [ -n "$netbird_path" ] && [ -f "$netbird_path/.env" ]; then
            echo "Login credentials:"
            cat "$netbird_path/.env"
        fi

        # Show domain info
        if [ -n "$netbird_path" ] && [ -f "$netbird_path/zitadel.env" ]; then
            local domain=$(grep "ZITADEL_EXTERNALDOMAIN" "$netbird_path/zitadel.env" 2>/dev/null | cut -d'=' -f2)
            if [ -n "$domain" ]; then
                echo ""
                echo "Access URL: https://$domain"
            fi
        fi

        return 0
    else
        echo "================================================="
        echo "Netbird restore completed with ERRORS!"
        echo "================================================="
        echo "Some components may not have been restored properly."
        echo "Check the output above for details."
        echo "You may need to:"
        echo "1. Check service logs: docker compose logs"
        echo "2. Restart services manually: docker compose restart"
        echo "3. Verify network connectivity and DNS settings"
        return 1
    fi
}

# Function to restore from domain-based backup with selection menu
restore_netbird_from_domain() {
    echo "Domain-Based Netbird Restore"
    echo "============================"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot proceed with domain restore."
        return 1
    fi

    # List available domains
    echo "Scanning for available domain backups..."
    local base_path="$RCLONE_REMOTE:/Backups/Netbird"
    local domains=()

    # Get list of directories (domains) in the backup location
    local domain_list=$(rclone lsd "$base_path" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -z "$domain_list" ]; then
        echo "No domain-based backups found."
        echo "Would you like to try restoring from legacy backup instead? (y/n)"
        read -rp "Choice: " use_legacy
        if [[ "$use_legacy" =~ ^[Yy]$ ]]; then
            return restore_netbird_full
        else
            return 1
        fi
    fi

    # Convert to array and display options
    local counter=1
    echo ""
    echo "Available domain backups:"
    echo "========================"

    while IFS= read -r domain; do
        if [ -n "$domain" ]; then
            domains+=("$domain")
            echo "$counter. $domain"

            # Show backup info for this domain
            local domain_path="$base_path/$domain"
            local file_count=$(rclone ls "$domain_path" 2>/dev/null | wc -l)
            local latest_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird.*backup.*\.tar\.gz" | sort -k2 -r | head -1 | awk '{print $2}')

            if [ -n "$latest_backup" ]; then
                local date_str=""
                if [[ "$latest_backup" =~ [0-9]{8}_[0-9]{6} ]]; then
                    local date_part=$(echo "$latest_backup" | grep -o '[0-9]\{8\}_[0-9]\{6\}')
                    date_str=$(echo "$date_part" | sed 's/\([0-9]\{4\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)_\([0-9]\{2\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1-\2-\3 \4:\5:\6/')
                fi
                echo "   Latest backup: $latest_backup"
                [ -n "$date_str" ] && echo "   Created: $date_str"
                echo "   Total files: $file_count"
            else
                echo "   No backup files found"
            fi
            echo ""
            ((counter++))
        fi
    done <<< "$domain_list"

    if [ ${#domains[@]} -eq 0 ]; then
        echo "No valid domain backups found."
        return 1
    fi

    # Get user selection
    echo "Select domain to restore from:"
    read -rp "Enter number (1-${#domains[@]}): " selection

    if ! [[ "$selection" =~ ^[0-9]+$ ]] || [ "$selection" -lt 1 ] || [ "$selection" -gt ${#domains[@]} ]; then
        echo "Invalid selection."
        return 1
    fi

    local selected_domain="${domains[$((selection-1))]}"
    echo ""
    echo "Selected domain: $selected_domain"
    echo ""

    # Confirm restore
    echo "WARNING: This will overwrite any existing Netbird installation!"
    echo "The restore will include:"
    echo "- Configuration files"
    echo "- Application data and databases"
    echo "- Docker volumes"
    echo "- Machine keys and certificates"
    echo ""
    read -rp "Are you sure you want to restore from domain '$selected_domain'? (y/n): " confirm_restore

    if [[ ! "$confirm_restore" =~ ^[Yy]$ ]]; then
        echo "Restore cancelled."
        return 1
    fi

    # Perform domain-specific restore
    echo ""
    echo "Starting restore from domain: $selected_domain"
    echo "=============================================="

    # Temporarily set the rclone source to the selected domain
    local original_rclone_path="$RCLONE_REMOTE:/Backups/Netbird"
    export TEMP_DOMAIN_RESTORE="$selected_domain"

    # Call the full restore function (it will use domain-specific paths)
    if restore_netbird_full_from_domain "$selected_domain"; then
        echo ""
        echo "========================================="
        echo "Domain restore completed successfully!"
        echo "========================================="
        echo "Restored from domain: $selected_domain"
        return 0
    else
        echo ""
        echo "========================================="
        echo "Domain restore failed!"
        echo "========================================="
        return 1
    fi
}

# Function to perform COMPLETE Netbird restore from specific domain
restore_netbird_full_from_domain() {
    local domain="$1"

    if [ -z "$domain" ]; then
        echo "Domain not specified for restore."
        return 1
    fi

    echo "Starting COMPLETE Netbird restore from domain: $domain"
    echo "======================================================"

    local success=true
    local domain_path="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    # Check if domain backup exists
    if ! rclone lsd "$RCLONE_REMOTE:/Backups/Netbird" 2>/dev/null | grep -q "$domain"; then
        echo "Domain backup not found: $domain"
        return 1
    fi

    echo "Restoring from domain path: $domain_path"
    echo ""

    # Restore configuration first
    echo "Step 1/4: Restoring configuration files..."
    if ! restore_netbird_config_from_domain "$domain"; then
        echo "Configuration restore failed."
        success=false
    fi
    echo ""

    # Restore Docker volumes
    echo "Step 2/4: Restoring Docker volumes..."
    if ! restore_netbird_volumes_from_domain "$domain"; then
        echo "Volumes restore failed."
        success=false
    fi
    echo ""

    # Restore application data
    echo "Step 3/4: Restoring application data..."
    if ! restore_netbird_data_from_domain "$domain"; then
        echo "Data restore failed."
        success=false
    fi
    echo ""

    # Final service restart and verification
    echo "Step 4/4: Final service restart and verification..."
    local netbird_path=$(detect_netbird_installation)
    if [ -n "$netbird_path" ]; then
        cd "$netbird_path"
        echo "Restarting all services with restored data..."
        docker compose down
        sleep 5
        docker compose up -d

        # Wait for services to start
        echo "Waiting for services to start..."
        sleep 30

        # Check if services are running
        if is_netbird_running; then
            echo "Services started successfully."
        else
            echo "Warning: Some services may not have started properly."
            success=false
        fi
    fi
    echo ""

    if $success; then
        echo "================================================="
        echo "COMPLETE domain restore finished successfully!"
        echo "================================================="
        echo "Restored from domain: $domain"
        echo "Restored components:"
        echo "✓ Configuration files"
        echo "✓ Management database"
        echo "✓ Zitadel identity provider database"
        echo "✓ Docker volumes (Caddy, certificates, etc.)"
        echo "✓ Machine keys and certificates"
        echo ""
        echo "Your Netbird installation should now be fully"
        echo "functional and identical to the backed up state."
        echo ""

        # Show access information
        if [ -n "$netbird_path" ] && [ -f "$netbird_path/.env" ]; then
            echo "Login credentials:"
            cat "$netbird_path/.env"
        fi

        # Show domain info
        if [ -n "$netbird_path" ] && [ -f "$netbird_path/zitadel.env" ]; then
            local restored_domain=$(grep "ZITADEL_EXTERNALDOMAIN" "$netbird_path/zitadel.env" 2>/dev/null | cut -d'=' -f2)
            if [ -n "$restored_domain" ]; then
                echo ""
                echo "Access URL: https://$restored_domain"
            fi
        fi

        return 0
    else
        echo "================================================="
        echo "Domain restore completed with ERRORS!"
        echo "================================================="
        echo "Some components may not have been restored properly."
        echo "Check the output above for details."
        return 1
    fi
}

# Helper function to restore configuration from specific domain
restore_netbird_config_from_domain() {
    local domain="$1"
    local backup_dir="/opt/netbird_backups"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Restoring configuration from domain: $domain"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Find the latest config backup file for this domain
    local config_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_config_backup_.*\.tar\.gz" | sort -k2 -r)

    if [ -z "$config_files" ]; then
        echo "No configuration backup found for domain: $domain"
        return 1
    fi

    local latest_config_file=$(echo "$config_files" | head -1 | awk '{print $2}')
    local temp_config_backup="$backup_dir/$latest_config_file"

    echo "Downloading configuration backup: $latest_config_file"
    rm -f "$temp_config_backup"

    if ! rclone copy "$rclone_source/$latest_config_file" "$backup_dir" --progress; then
        echo "Failed to download configuration backup."
        return 1
    fi

    if [ ! -f "$temp_config_backup" ]; then
        echo "Configuration backup file not found after download."
        return 1
    fi

    # Ask for installation directory
    echo "Where would you like to restore the Netbird configuration?"
    read -rp "Enter path (default: /opt/netbird): " restore_path
    restore_path=${restore_path:-/opt/netbird}

    # Create restore directory
    mkdir -p "$restore_path"

    # Extract configuration files
    echo "Extracting configuration files..."
    cd "$restore_path"
    if tar -xzf "$temp_config_backup"; then
        echo "Configuration restored successfully to $restore_path"
        rm -f "$temp_config_backup"
        return 0
    else
        echo "Failed to extract configuration files."
        rm -f "$temp_config_backup"
        return 1
    fi
}

# Helper function to restore volumes from specific domain
restore_netbird_volumes_from_domain() {
    local domain="$1"
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Restoring Docker volumes from domain: $domain"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Find the latest volumes backup file for this domain
    local volumes_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_volumes_backup_.*\.tar\.gz" | sort -k2 -r)

    if [ -z "$volumes_files" ]; then
        echo "Volumes backup not found for domain: $domain"
        echo "Skipping volume restoration..."
        return 0  # Return success since this is not critical
    fi

    local latest_volumes_file=$(echo "$volumes_files" | head -1 | awk '{print $2}')
    local temp_volumes_backup="$backup_dir/$latest_volumes_file"

    echo "Downloading volumes backup: $latest_volumes_file"
    rm -f "$temp_volumes_backup"

    if ! rclone copy "$rclone_source/$latest_volumes_file" "$backup_dir" --progress; then
        echo "Failed to download volumes backup."
        echo "Skipping volume restoration..."
        return 0  # Return success since this is not critical
    fi

    if [ ! -f "$temp_volumes_backup" ]; then
        echo "Volumes backup file not found after download."
        echo "Skipping volume restoration..."
        return 0  # Return success since this is not critical
    fi

    # Stop all services
    echo "Stopping all Netbird services..."
    cd "$netbird_path"
    docker compose down

    # Extract volumes backup
    echo "Extracting volumes backup..."
    local temp_volumes_dir="$backup_dir/volumes_restore_temp"
    mkdir -p "$temp_volumes_dir"

    cd "$temp_volumes_dir"
    if tar -xzf "$temp_volumes_backup"; then
        echo "Restoring Docker volumes..."

        # Restore each volume
        for volume_dir in */; do
            if [ -d "$volume_dir" ]; then
                local volume_name=$(basename "$volume_dir")
                echo "Restoring volume: $volume_name"

                # Create volume if it doesn't exist
                docker volume create "$volume_name" >/dev/null 2>&1

                # Restore volume data
                if [ -f "$volume_dir/volume_data.tar.gz" ]; then
                    docker run --rm -v "$volume_name:/volume" -v "$(pwd)/$volume_dir:/backup" alpine:latest \
                        sh -c "cd /volume && tar -xzf /backup/volume_data.tar.gz" 2>/dev/null || {
                        echo "Warning: Failed to restore volume $volume_name"
                    }
                fi
            fi
        done

        echo "Volumes restored successfully."
        rm -f "$temp_volumes_backup"
        rm -rf "$temp_volumes_dir"

        # Start services
        echo "Starting Netbird services..."
        cd "$netbird_path"
        docker compose up -d
        return 0
    else
        echo "Failed to extract volumes backup."
        rm -f "$temp_volumes_backup"
        rm -rf "$temp_volumes_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Helper function to restore data from specific domain
restore_netbird_data_from_domain() {
    local domain="$1"
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Restoring application data from domain: $domain"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Find the latest data backup file for this domain
    local data_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_data_backup_.*\.tar\.gz" | sort -k2 -r)

    if [ -z "$data_files" ]; then
        echo "No data backup found for domain: $domain"
        return 1
    fi

    local latest_data_file=$(echo "$data_files" | head -1 | awk '{print $2}')
    local temp_data_backup="$backup_dir/$latest_data_file"

    echo "Downloading data backup: $latest_data_file"
    rm -f "$temp_data_backup"

    if ! rclone copy "$rclone_source/$latest_data_file" "$backup_dir" --progress; then
        echo "Failed to download data backup."
        return 1
    fi

    if [ ! -f "$temp_data_backup" ]; then
        echo "Data backup file not found after download."
        return 1
    fi

    # Stop all services for consistent restore
    echo "Stopping all services for data restoration..."
    cd "$netbird_path"
    docker compose down

    # Extract and restore data
    echo "Extracting and restoring application data..."
    local temp_restore_dir="$backup_dir/data_restore_temp"
    mkdir -p "$temp_restore_dir"

    cd "$temp_restore_dir"
    if tar -xzf "$temp_data_backup"; then
        # Restore management data
        if [ -d "management_data" ]; then
            echo "Restoring management data..."
            # Start only management container to restore data
            cd "$netbird_path"
            docker compose up -d management
            sleep 10

            # Copy management data
            if docker compose cp -a "$temp_restore_dir/management_data/." management:/var/lib/netbird/; then
                echo "Management data restored successfully."
            else
                echo "Warning: Failed to restore management data."
            fi
            docker compose stop management
        fi

        # Restore Zitadel database
        if [ -d "zitadel_db" ]; then
            echo "Restoring Zitadel database..."
            cd "$netbird_path"

            # Start database container
            local db_container=$(docker compose config --services | grep -E "postgres|zdb" | head -1)
            if [ -n "$db_container" ]; then
                docker compose up -d "$db_container"
                echo "Waiting for database to be ready..."
                sleep 20

                local restore_success=false

                # Method 1: Try pg_dumpall restore
                if [ -f "$temp_restore_dir/zitadel_db/zitadel_backup.sql" ]; then
                    echo "Attempting to restore database with pg_dumpall backup..."
                    if docker compose exec -T "$db_container" psql -U postgres < "$temp_restore_dir/zitadel_db/zitadel_backup.sql" >/dev/null 2>&1; then
                        echo "Zitadel database restored successfully with pg_dumpall."
                        restore_success=true
                    fi
                fi

                # Method 2: Try individual database restore
                if ! $restore_success && [ -f "$temp_restore_dir/zitadel_db/zitadel_db_backup.sql" ]; then
                    echo "Attempting to restore individual database..."
                    # Create database if it doesn't exist
                    docker compose exec -T "$db_container" psql -U postgres -c "CREATE DATABASE zitadel;" 2>/dev/null
                    if docker compose exec -T "$db_container" psql -U postgres -d zitadel < "$temp_restore_dir/zitadel_db/zitadel_db_backup.sql" >/dev/null 2>&1; then
                        echo "Zitadel database restored successfully with pg_dump."
                        restore_success=true
                    fi
                fi

                # Method 3: Try data directory restore
                if ! $restore_success && [ -f "$temp_restore_dir/zitadel_db/postgres_data.tar.gz" ]; then
                    echo "Attempting to restore database data directory..."
                    docker compose stop "$db_container"
                    sleep 5

                    # Copy data directory backup to container
                    docker compose cp "$temp_restore_dir/zitadel_db/postgres_data.tar.gz" "$db_container:/tmp/" 2>/dev/null
                    docker compose start "$db_container"
                    sleep 10

                    if docker compose exec -T "$db_container" sh -c "cd / && tar -xzf /tmp/postgres_data.tar.gz" 2>/dev/null; then
                        echo "Database data directory restored successfully."
                        restore_success=true
                    fi
                fi

                if $restore_success; then
                    echo "Zitadel database restored successfully."
                else
                    echo "Warning: All database restore methods failed."
                fi

                docker compose stop "$db_container"
            else
                echo "Warning: No database container found for restore."
            fi
        else
            echo "No Zitadel database backup found to restore."
        fi

        # Restore machine keys and certificates
        if [ -d "machinekey" ]; then
            echo "Restoring machine keys and certificates..."
            cd "$netbird_path"
            cp -r "$temp_restore_dir/machinekey" . 2>/dev/null || echo "Warning: Failed to restore machine keys."
        fi

        # Restore additional data directories
        cd "$netbird_path"
        for dir in "certs" "ssl" "data" "logs"; do
            if [ -d "$temp_restore_dir/$dir" ]; then
                cp -r "$temp_restore_dir/$dir" . 2>/dev/null
            fi
        done

        echo "Application data restored successfully."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"

        # Start all services
        echo "Starting all Netbird services..."
        docker compose up -d
        return 0
    else
        echo "Failed to extract data backup."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to test backup components before running full backup
test_backup_components() {
    echo "Testing Netbird Backup Components"
    echo "================================="

    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "✗ Netbird installation not found."
        return 1
    fi

    echo "✓ Netbird installation found at: $netbird_path"
    cd "$netbird_path"

    echo ""
    echo "1. Testing Configuration Files:"
    echo "------------------------------"
    local config_files=("docker-compose.yml" "management.json" "turnserver.conf" "setup.env" "Caddyfile" "zitadel.env")
    local config_count=0
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            echo "✓ $file ($(stat -c%s "$file" 2>/dev/null || echo "unknown") bytes)"
            ((config_count++))
        else
            echo "✗ $file (missing)"
        fi
    done
    echo "Configuration files found: $config_count/${#config_files[@]}"

    echo ""
    echo "2. Testing Docker Services:"
    echo "---------------------------"
    local services=$(docker compose config --services 2>/dev/null | tr '\n' ' ')
    if [ -n "$services" ]; then
        echo "✓ Services found: $services"
        for service in $services; do
            if docker compose ps "$service" 2>/dev/null | grep -q "Up"; then
                echo "  ✓ $service: Running"
            else
                echo "  ✗ $service: Not running"
            fi
        done
    else
        echo "✗ No services found in docker-compose.yml"
    fi

    echo ""
    echo "3. Testing Docker Volumes:"
    echo "-------------------------"
    local compose_volumes=$(docker compose config --volumes 2>/dev/null | tr '\n' ' ')
    local system_volumes=$(docker volume ls --format "{{.Name}}" | grep -E "netbird|caddy|postgres|zitadel" | tr '\n' ' ')
    local all_volumes="$compose_volumes $system_volumes"
    all_volumes=$(echo "$all_volumes" | tr ' ' '\n' | sort -u | grep -v '^$' | tr '\n' ' ')

    if [ -n "$all_volumes" ]; then
        echo "✓ Volumes found: $all_volumes"
        for volume in $all_volumes; do
            volume=$(echo "$volume" | tr -d ' ')
            if [ -n "$volume" ] && docker volume inspect "$volume" >/dev/null 2>&1; then
                local size=$(docker run --rm -v "$volume:/volume" alpine:latest du -sh /volume 2>/dev/null | cut -f1 || echo "unknown")
                echo "  ✓ $volume: $size"
            else
                echo "  ✗ $volume: Not accessible"
            fi
        done
    else
        echo "✗ No volumes found"
    fi

    echo ""
    echo "4. Testing Database Connection:"
    echo "------------------------------"
    local db_container=""

    # Use same detection logic as backup function
    if docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "zdb"; then
        db_container="zdb"
    elif docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "postgres"; then
        db_container="postgres"
    elif docker compose config --services 2>/dev/null | grep -q "zdb"; then
        db_container="zdb"
    elif docker compose config --services 2>/dev/null | grep -q "postgres"; then
        db_container="postgres"
    fi

    if [ -n "$db_container" ]; then
        echo "✓ Database container found: $db_container"
        if docker compose ps "$db_container" 2>/dev/null | grep -q "Up"; then
            echo "  ✓ Database is running"
            if docker compose exec -T "$db_container" psql -U postgres -c "SELECT version();" >/dev/null 2>&1; then
                echo "  ✓ Database connection successful"
                local db_list=$(docker compose exec -T "$db_container" psql -U postgres -c "\l" 2>/dev/null | grep -c "zitadel" || echo "0")
                echo "  ✓ Zitadel databases found: $db_list"
            else
                echo "  ✗ Database connection failed"
            fi
        else
            echo "  ✗ Database is not running"
        fi
    else
        echo "✗ No database container found"
    fi

    echo ""
    echo "5. Testing Cloud Storage:"
    echo "------------------------"
    if install_rclone >/dev/null 2>&1; then
        echo "✓ Rclone is available"
        if rclone lsd "$RCLONE_REMOTE:" >/dev/null 2>&1; then
            echo "✓ Cloud storage connection successful"
            if rclone lsd "$RCLONE_REMOTE:/Backups/Netbird" >/dev/null 2>&1; then
                echo "✓ Netbird backup directory exists"
            else
                echo "! Netbird backup directory doesn't exist (will be created)"
            fi
        else
            echo "✗ Cloud storage connection failed"
        fi
    else
        echo "✗ Rclone is not available"
    fi

    echo ""
    echo "6. Testing Backup Directory:"
    echo "---------------------------"
    local backup_dir="/opt/netbird_backups"
    if mkdir -p "$backup_dir" 2>/dev/null; then
        echo "✓ Backup directory accessible: $backup_dir"
        local free_space=$(df "$backup_dir" | awk 'NR==2 {print $4}')
        echo "✓ Available space: $(($free_space / 1024)) MB"
    else
        echo "✗ Cannot create backup directory"
    fi

    echo ""
    echo "Test Summary:"
    echo "============"
    echo "Run this test before attempting backup to identify potential issues."
    echo "If any tests fail, resolve those issues before running backup."
    echo ""
    read -rp "Press Enter to continue..."
}
