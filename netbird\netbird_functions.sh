#!/bin/bash

# Function to check if Netbird containers are running
is_netbird_running() {
    local containers=("management" "signal" "dashboard" "coturn" "relay" "zitadel" "zdb" "caddy")
    local running_count=0

    for container in "${containers[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "${container}"; then
            ((running_count++))
        fi
    done

    # Return true if at least 6 core containers are running (management, signal, dashboard, zitadel, zdb, caddy)
    if [ $running_count -ge 6 ]; then
        return 0
    else
        return 1
    fi
}

# Function to detect Netbird installation directory
detect_netbird_installation() {
    local possible_paths=(
        "/opt/netbird"
        "/home/<USER>/netbird"
        "/root/netbird"
        "$(pwd)/netbird"
        "/var/lib/netbird"
        "/usr/local/netbird"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -d "$path" ] && [ -f "$path/docker-compose.yml" ]; then
            echo "$path"
            return 0
        fi
    done
    
    # Try to find docker-compose.yml with netbird services
    local compose_files=$(find / -name "docker-compose.yml" -type f 2>/dev/null | head -20)
    for file in $compose_files; do
        if grep -q "netbird\|management\|signal" "$file" 2>/dev/null; then
            echo "$(dirname "$file")"
            return 0
        fi
    done
    
    return 1
}

# Function to check status of all Netbird services
check_netbird_status() {
    echo "Checking Netbird services status..."
    echo "=================================="
    
    local containers=("management" "signal" "dashboard" "coturn" "relay" "zitadel" "zdb" "caddy")
    local all_running=true

    for container in "${containers[@]}"; do
        echo -n "Checking $container: "
        if docker ps --format "{{.Names}}" | grep -q "${container}"; then
            local status=$(docker ps --format "{{.Names}} {{.Status}}" | grep "${container}" | awk '{print $2}')
            echo -e "${GREEN}Running${NC} ($status)"
        else
            echo -e "${RED}Not Running${NC}"
            all_running=false
        fi
    done
    
    echo "=================================="
    if $all_running; then
        echo -e "${GREEN}All Netbird services are running${NC}"
    else
        echo -e "${YELLOW}Some Netbird services are not running${NC}"
    fi
    
    return 0
}

# Function to stop Netbird services
stop_netbird_services() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Cannot stop services."
        return 1
    fi
    
    echo "Stopping Netbird services..."
    cd "$netbird_path"
    
    if [ -f "docker-compose.yml" ]; then
        docker compose stop
        echo "Netbird services stopped."
        return 0
    else
        echo "docker-compose.yml not found in $netbird_path"
        return 1
    fi
}

# Function to start Netbird services
start_netbird_services() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Cannot start services."
        return 1
    fi
    
    echo "Starting Netbird services..."
    cd "$netbird_path"
    
    if [ -f "docker-compose.yml" ]; then
        docker compose up -d
        echo "Netbird services started."
        return 0
    else
        echo "docker-compose.yml not found in $netbird_path"
        return 1
    fi
}

# Function to restart Netbird services
restart_netbird_services() {
    echo "Restarting Netbird services..."
    
    if stop_netbird_services; then
        sleep 5
        if start_netbird_services; then
            echo "Netbird services restarted successfully."
            return 0
        fi
    fi
    
    echo "Failed to restart Netbird services."
    return 1
}

# Function to view Netbird logs
view_netbird_logs() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    cd "$netbird_path"
    
    echo "Select which service logs to view:"
    echo "1. Management"
    echo "2. Signal"
    echo "3. Dashboard"
    echo "4. Coturn"
    echo "5. Relay"
    echo "6. All services"
    
    read -rp "Enter your choice (1-6): " log_choice
    
    case $log_choice in
        1) docker compose logs management ;;
        2) docker compose logs signal ;;
        3) docker compose logs dashboard ;;
        4) docker compose logs coturn ;;
        5) docker compose logs relay ;;
        6) docker compose logs ;;
        *) echo "Invalid choice." ;;
    esac
}

# Function to get Netbird container info
get_netbird_container_info() {
    echo "Netbird Container Information:"
    echo "============================="
    
    local containers=("management" "signal" "dashboard" "coturn" "relay" "zitadel" "zdb" "caddy")

    for container in "${containers[@]}"; do
        echo "--- $container ---"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" | grep "${container}" || echo "Container not found"
        echo
    done
}

# Function to check Netbird network connectivity
check_netbird_connectivity() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    echo "Checking Netbird connectivity..."
    echo "==============================="
    
    # Check if management API is accessible
    echo "Testing Management API..."
    if curl -s -k https://localhost:443/api/status >/dev/null 2>&1; then
        echo -e "${GREEN}Management API: Accessible${NC}"
    else
        echo -e "${RED}Management API: Not accessible${NC}"
    fi
    
    # Check if signal service is accessible
    echo "Testing Signal service..."
    if netstat -tuln | grep -q ":33073"; then
        echo -e "${GREEN}Signal service: Port 33073 is open${NC}"
    else
        echo -e "${RED}Signal service: Port 33073 is not accessible${NC}"
    fi
    
    # Check if dashboard is accessible
    echo "Testing Dashboard..."
    if curl -s http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}Dashboard: Accessible${NC}"
    else
        echo -e "${RED}Dashboard: Not accessible${NC}"
    fi
    
    # Check TURN server
    echo "Testing TURN server..."
    if netstat -tuln | grep -q ":3478"; then
        echo -e "${GREEN}TURN server: Port 3478 is open${NC}"
    else
        echo -e "${RED}TURN server: Port 3478 is not accessible${NC}"
    fi
}

# Function to check Netbird installation prerequisites
check_netbird_prerequisites() {
    echo "Checking Netbird installation prerequisites..."

    local missing_requirements=()

    # Check if running as root or with sudo access
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        echo "This script requires root privileges or sudo access."
        return 1
    fi

    # Check available disk space (need at least 2GB)
    local available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 2097152 ]; then  # 2GB in KB
        missing_requirements+=("Insufficient disk space (need at least 2GB)")
    fi

    # Check if ports are available
    local required_ports=(80 443 33073 10000 33080 3478)
    for port in "${required_ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            missing_requirements+=("Port $port is already in use")
        fi
    done

    if [ ${#missing_requirements[@]} -gt 0 ]; then
        echo "Prerequisites check failed:"
        printf '%s\n' "${missing_requirements[@]}"
        return 1
    fi

    echo "Prerequisites check passed."
    return 0
}

# Function to install Netbird
install_netbird() {
    local install_path="/opt/netbird"

    echo "Installing Netbird..."
    echo "===================="

    # Check prerequisites first
    if ! check_netbird_prerequisites; then
        echo "Prerequisites check failed. Please resolve the issues above."
        return 1
    fi

    # Check if already installed
    if [ -d "$install_path" ] && [ -f "$install_path/docker-compose.yml" ]; then
        echo "Netbird appears to be already installed at $install_path"
        read -rp "Would you like to reinstall? This will remove the existing installation. (y/n): " reinstall_choice
        if [[ "$reinstall_choice" =~ ^[Yy]$ ]]; then
            if ! remove_netbird; then
                echo "Failed to remove existing installation."
                return 1
            fi
        else
            echo "Installation cancelled."
            return 1
        fi
    fi

    # Ensure Docker is installed
    if ! install_docker; then
        echo "Docker installation failed. Cannot proceed."
        return 1
    fi

    # Check required tools
    if ! command -v curl >/dev/null 2>&1; then
        echo "Installing curl..."
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y curl
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y curl
        else
            echo "Please install curl manually."
            return 1
        fi
    fi

    if ! command -v jq >/dev/null 2>&1; then
        echo "Installing jq..."
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y jq
        else
            echo "Please install jq manually."
            return 1
        fi
    fi

    # Get domain name
    echo ""
    echo "Netbird requires a domain name for proper operation."
    echo "The domain should point to this server's public IP address."
    echo "Example: netbird.yourdomain.com"
    echo ""

    local netbird_domain=""
    while [ -z "$netbird_domain" ]; do
        read -rp "Enter your domain name: " netbird_domain

        if [ -z "$netbird_domain" ]; then
            echo "Domain name is required. Please enter a valid domain."
            continue
        fi

        # Basic domain validation
        if [[ ! "$netbird_domain" =~ ^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            echo "Invalid domain format. Please use a valid domain name (e.g., netbird.example.com)."
            netbird_domain=""
            continue
        fi

        # Check if domain is reachable (optional warning)
        echo "Checking domain accessibility..."
        if ! nslookup "$netbird_domain" >/dev/null 2>&1; then
            echo "Warning: Domain '$netbird_domain' does not resolve to an IP address."
            read -rp "Continue anyway? (y/n): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                netbird_domain=""
                continue
            fi
        fi

        echo "Using domain: $netbird_domain"
        break
    done

    # Create installation directory
    echo "Creating installation directory at $install_path..."
    sudo mkdir -p "$install_path"

    # Change to installation directory
    cd "$install_path" || {
        echo "Failed to change to installation directory."
        return 1
    }

    # Set environment variable and download installation script
    echo "Downloading and running Netbird installation script..."
    echo "This may take several minutes..."

    export NETBIRD_DOMAIN="$netbird_domain"

    # Download and run the official installation script with error handling
    echo "Downloading installation script..."
    local install_script="/tmp/netbird-install.sh"

    if curl -fsSL https://github.com/netbirdio/netbird/releases/latest/download/getting-started-with-zitadel.sh -o "$install_script"; then
        echo "Running installation script..."
        chmod +x "$install_script"
        if sudo -E bash "$install_script"; then
        echo ""
        echo "Netbird installation completed successfully!"
        echo "========================================"
        echo "Domain: $netbird_domain"
        echo "Installation path: $install_path"
        echo ""
        echo "You can access Netbird at: https://$netbird_domain"
        echo ""

        # Look for credentials file
        if [ -f "$install_path/.env" ]; then
            echo "Login credentials:"
            cat "$install_path/.env"
        else
            echo "Check the installation directory for login credentials."
        fi

            echo ""
            echo "Installation completed. You can now manage Netbird through this menu."
            rm -f "$install_script"
            return 0
        else
            echo "Netbird installation script failed."
            echo "Check the output above for error details."
            rm -f "$install_script"
            return 1
        fi
    else
        echo "Failed to download Netbird installation script."
        echo "Please check your internet connection and try again."
        return 1
    fi
}

# Function to remove Netbird installation
remove_netbird() {
    echo "Removing Netbird installation..."
    echo "==============================="

    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    echo "Found Netbird installation at: $netbird_path"

    # Confirm removal
    echo "WARNING: This will completely remove Netbird and all its data!"
    read -rp "Are you sure you want to continue? (y/n): " confirm_removal
    if [[ ! "$confirm_removal" =~ ^[Yy]$ ]]; then
        echo "Removal cancelled."
        return 1
    fi

    # Offer backup before removal
    read -rp "Would you like to create a backup before removal? (y/n): " backup_choice
    if [[ "$backup_choice" =~ ^[Yy]$ ]]; then
        echo "Creating backup before removal..."
        if ! backup_netbird_full; then
            read -rp "Backup failed. Continue with removal anyway? (y/n): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                echo "Removal cancelled."
                return 1
            fi
        fi
    fi

    cd "$netbird_path"

    # Stop and remove containers
    echo "Stopping and removing Netbird containers..."
    if [ -f "docker-compose.yml" ]; then
        docker compose down --volumes --remove-orphans
    fi

    # Remove Docker images
    echo "Removing Netbird Docker images..."
    docker images | grep -E "netbird|zitadel|caddy|postgres|cockroach" | awk '{print $3}' | xargs -r docker rmi --force

    # Remove installation directory
    echo "Removing installation directory..."
    cd /
    sudo rm -rf "$netbird_path"

    # Remove backup directory if empty
    if [ -d "/opt/netbird_backups" ] && [ -z "$(ls -A /opt/netbird_backups)" ]; then
        echo "Removing empty backup directory..."
        sudo rm -rf "/opt/netbird_backups"
    fi

    echo "Netbird has been completely removed."
    return 0
}

# Function to update Netbird installation
update_netbird() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    echo "Updating Netbird installation..."
    echo "==============================="

    # Backup before update
    read -rp "Would you like to create a backup before updating? (y/n): " backup_choice
    if [[ "$backup_choice" =~ ^[Yy]$ ]]; then
        if ! backup_netbird_full; then
            echo "Backup failed. Update cancelled."
            return 1
        fi
    fi

    cd "$netbird_path"

    # Pull latest images
    echo "Pulling latest Netbird images..."
    docker compose pull

    # Restart with new images
    echo "Restarting services with new images..."
    docker compose up -d --force-recreate

    echo "Netbird update completed."
    return 0
}

# Function to debug Netbird installation and volumes
debug_netbird_installation() {
    echo "Netbird Installation Debug Information"
    echo "====================================="

    local netbird_path=$(detect_netbird_installation)

    if [ -n "$netbird_path" ]; then
        echo "✓ Installation found at: $netbird_path"
        cd "$netbird_path"

        echo ""
        echo "Docker Compose Services:"
        echo "------------------------"
        docker compose config --services 2>/dev/null || echo "Failed to get services"

        echo ""
        echo "Docker Compose Volumes:"
        echo "----------------------"
        docker compose config --volumes 2>/dev/null || echo "No volumes in compose config"

        echo ""
        echo "All Docker Volumes (filtered for Netbird):"
        echo "------------------------------------------"
        docker volume ls --format "{{.Name}}" | grep -E "netbird|caddy|postgres|zitadel" || echo "No matching volumes found"

        echo ""
        echo "Running Containers:"
        echo "------------------"
        docker compose ps 2>/dev/null || echo "Failed to get container status"

        echo ""
        echo "Container Images:"
        echo "----------------"
        docker compose images 2>/dev/null || echo "Failed to get images"

        echo ""
        echo "Configuration Files:"
        echo "-------------------"
        for file in docker-compose.yml management.json turnserver.conf setup.env Caddyfile zitadel.env dashboard.env; do
            if [ -f "$file" ]; then
                echo "✓ $file ($(stat -c%s "$file" 2>/dev/null || echo "unknown") bytes)"
            else
                echo "✗ $file (missing)"
            fi
        done

        echo ""
        echo "Directory Contents:"
        echo "------------------"
        ls -la

    else
        echo "✗ Netbird installation not found"
        echo ""
        echo "Searching for docker-compose.yml files with Netbird services..."
        find /opt /home /root -name "docker-compose.yml" -type f 2>/dev/null | while read -r file; do
            if grep -q "netbird\|management\|signal" "$file" 2>/dev/null; then
                echo "Found potential Netbird installation: $(dirname "$file")"
            fi
        done
    fi

    echo ""
    echo "System Information:"
    echo "------------------"
    echo "Docker version: $(docker --version 2>/dev/null || echo "Not installed")"
    echo "Docker Compose version: $(docker compose version 2>/dev/null || echo "Not installed")"
    echo "Available disk space: $(df -h / | awk 'NR==2 {print $4}')"
    echo "Memory usage: $(free -h | awk 'NR==2{printf "%.1f/%.1f GB (%.2f%%)\n", $3/1024/1024, $2/1024/1024, $3*100/$2}')"
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to test database connection specifically
test_netbird_database() {
    echo "Testing Netbird Database Connection"
    echo "=================================="

    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "✗ Netbird installation not found."
        return 1
    fi

    cd "$netbird_path"

    echo "1. Container Detection:"
    echo "----------------------"
    echo "Running containers:"
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep -E "zdb|postgres"

    echo ""
    echo "2. Service Detection:"
    echo "--------------------"
    echo "Docker compose services:"
    docker compose config --services 2>/dev/null | grep -E "zdb|postgres" || echo "No database services found"

    echo ""
    echo "3. Database Connection Test:"
    echo "---------------------------"

    # Find database container using same logic as backup
    local db_container=""
    if docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^zdb$"; then
        db_container="zdb"
    elif docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^postgres$"; then
        db_container="postgres"
    elif docker compose config --services 2>/dev/null | grep -q "^zdb$"; then
        db_container="zdb"
    elif docker compose config --services 2>/dev/null | grep -q "^postgres$"; then
        db_container="postgres"
    elif docker ps --format "{{.Names}}" | grep -q "zdb"; then
        db_container="zdb"
    elif docker ps --format "{{.Names}}" | grep -q "postgres"; then
        db_container="postgres"
    fi

    if [ -n "$db_container" ]; then
        echo "✓ Database container found: $db_container"

        # Test if container is running
        if docker compose ps "$db_container" 2>/dev/null | grep -q "Up"; then
            echo "✓ Database container is running"

            # Test database connection
            echo "Testing PostgreSQL connection..."
            if docker compose exec -T "$db_container" psql -U postgres -c "SELECT version();" 2>/dev/null; then
                echo "✓ Database connection successful"

                # List databases
                echo ""
                echo "Available databases:"
                docker compose exec -T "$db_container" psql -U postgres -c "\l" 2>/dev/null || echo "Failed to list databases"

                # Test backup command
                echo ""
                echo "Testing backup command..."
                if docker compose exec -T "$db_container" pg_dumpall -U postgres --version 2>/dev/null; then
                    echo "✓ pg_dumpall command available"
                else
                    echo "✗ pg_dumpall command failed"
                fi

            else
                echo "✗ Database connection failed"
            fi
        else
            echo "✗ Database container is not running"
        fi
    else
        echo "✗ No database container found"
    fi

    echo ""
    echo "4. Volume Check:"
    echo "---------------"
    echo "Database-related volumes:"
    docker volume ls | grep -E "zdb|postgres" || echo "No database volumes found"
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to detect domain name from Netbird configuration
detect_netbird_domain() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo ""
        return 1
    fi

    cd "$netbird_path"

    # Try multiple methods to detect domain
    local domain=""

    # Method 1: Check zitadel.env for ZITADEL_EXTERNALDOMAIN
    if [ -f "zitadel.env" ]; then
        domain=$(grep "ZITADEL_EXTERNALDOMAIN" "zitadel.env" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain" ]; then
            echo "$domain"
            return 0
        fi
    fi

    # Method 2: Check setup.env for NETBIRD_DOMAIN
    if [ -f "setup.env" ]; then
        domain=$(grep "NETBIRD_DOMAIN" "setup.env" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain" ]; then
            echo "$domain"
            return 0
        fi
    fi

    # Method 3: Check docker-compose.yml for domain references
    if [ -f "docker-compose.yml" ]; then
        domain=$(grep -E "NETBIRD_DOMAIN|ZITADEL_EXTERNALDOMAIN" "docker-compose.yml" 2>/dev/null | head -1 | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain" ]; then
            echo "$domain"
            return 0
        fi
    fi

    # Method 4: Check Caddyfile for domain
    if [ -f "Caddyfile" ]; then
        domain=$(grep -E "^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}" "Caddyfile" 2>/dev/null | head -1 | awk '{print $1}' | xargs)
        if [ -n "$domain" ]; then
            echo "$domain"
            return 0
        fi
    fi

    # Method 5: Check .env file for domain
    if [ -f ".env" ]; then
        domain=$(grep -E "DOMAIN|NETBIRD_DOMAIN" ".env" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain" ]; then
            echo "$domain"
            return 0
        fi
    fi

    # If no domain found, return empty
    echo ""
    return 1
}

# Function to list available domain backups from cloud storage
list_domain_backups() {
    echo "Scanning for domain-based backups..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot list domain backups."
        return 1
    fi

    local base_path="$RCLONE_REMOTE:/Backups/Netbird"
    local domains=()

    # Get list of directories (domains) in the backup location
    local domain_list=$(rclone lsd "$base_path" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -z "$domain_list" ]; then
        echo "No domain-based backups found in cloud storage."
        echo "Checking for legacy backups..."

        # Check for legacy backup files (without domain structure)
        if rclone ls "$base_path" 2>/dev/null | grep -q "netbird.*backup"; then
            echo "Found legacy backup files (not organized by domain):"
            rclone ls "$base_path" 2>/dev/null | grep "netbird.*backup" | while read -r size file; do
                echo "  - $file (Size: $size bytes)"
            done
            echo ""
            echo "Note: Legacy backups can still be restored using the standard restore options."
        else
            echo "No backups found at all."
        fi
        return 1
    fi

    # Convert to array
    while IFS= read -r domain; do
        if [ -n "$domain" ]; then
            domains+=("$domain")
        fi
    done <<< "$domain_list"

    if [ ${#domains[@]} -eq 0 ]; then
        echo "No domain backups found."
        return 1
    fi

    echo "Available domain backups:"
    echo "========================"

    for i in "${!domains[@]}"; do
        local domain="${domains[i]}"
        echo ""
        echo "$((i+1)). Domain: $domain"
        echo "   Backup files:"

        # List files for this domain
        local domain_path="$base_path/$domain"
        local files=$(rclone ls "$domain_path" 2>/dev/null)

        if [ -n "$files" ]; then
            echo "$files" | while read -r size file; do
                local date_str=""
                # Try to extract date from filename
                if [[ "$file" =~ [0-9]{8}_[0-9]{6} ]]; then
                    local date_part=$(echo "$file" | grep -o '[0-9]\{8\}_[0-9]\{6\}')
                    local date_formatted=$(echo "$date_part" | sed 's/\([0-9]\{4\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)_\([0-9]\{2\}\)\([0-9]\{2\}\)\([0-9]\{2\}\)/\1-\2-\3 \4:\5:\6/')
                    date_str=" (Created: $date_formatted)"
                fi
                echo "     - $file ($(numfmt --to=iec $size))$date_str"
            done
        else
            echo "     - No files found"
        fi
    done

    echo ""
    return 0
}

# Function to test the is_netbird_running function specifically
test_is_netbird_running() {
    echo "Testing is_netbird_running() Function"
    echo "===================================="

    echo "Current running containers:"
    docker ps --format "{{.Names}}" | grep -E "netbird|management|signal|dashboard|coturn|relay|zitadel|zdb|caddy"

    echo ""
    echo "Testing container detection:"
    local containers=("management" "signal" "dashboard" "coturn" "relay" "zitadel" "zdb" "caddy")
    local running_count=0

    for container in "${containers[@]}"; do
        echo -n "Checking $container: "
        if docker ps --format "{{.Names}}" | grep -q "${container}"; then
            echo "✓ Found"
            ((running_count++))
        else
            echo "✗ Not found"
        fi
    done

    echo ""
    echo "Total containers found: $running_count/8"
    echo "Required for success: 6+"

    echo ""
    echo "Function result:"
    if is_netbird_running; then
        echo "✓ is_netbird_running() returns SUCCESS"
    else
        echo "✗ is_netbird_running() returns FAILURE"
    fi

    echo ""
    read -rp "Press Enter to continue..."
}

# Function to test domain detection
test_domain_detection() {
    echo "Testing Netbird Domain Detection"
    echo "==============================="

    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "✗ Netbird installation not found."
        echo "Cannot test domain detection without an installation."
        echo ""
        read -rp "Press Enter to continue..."
        return 1
    fi

    echo "✓ Netbird installation found at: $netbird_path"
    echo ""

    cd "$netbird_path"

    echo "Checking configuration files for domain information:"
    echo "==================================================="

    # Check each method
    echo ""
    echo "Method 1: Checking zitadel.env for ZITADEL_EXTERNALDOMAIN"
    if [ -f "zitadel.env" ]; then
        echo "✓ zitadel.env exists"
        local domain1=$(grep "ZITADEL_EXTERNALDOMAIN" "zitadel.env" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain1" ]; then
            echo "✓ Found domain: $domain1"
        else
            echo "✗ No ZITADEL_EXTERNALDOMAIN found"
        fi
    else
        echo "✗ zitadel.env not found"
    fi

    echo ""
    echo "Method 2: Checking setup.env for NETBIRD_DOMAIN"
    if [ -f "setup.env" ]; then
        echo "✓ setup.env exists"
        local domain2=$(grep "NETBIRD_DOMAIN" "setup.env" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain2" ]; then
            echo "✓ Found domain: $domain2"
        else
            echo "✗ No NETBIRD_DOMAIN found"
        fi
    else
        echo "✗ setup.env not found"
    fi

    echo ""
    echo "Method 3: Checking docker-compose.yml for domain references"
    if [ -f "docker-compose.yml" ]; then
        echo "✓ docker-compose.yml exists"
        local domain3=$(grep -E "NETBIRD_DOMAIN|ZITADEL_EXTERNALDOMAIN" "docker-compose.yml" 2>/dev/null | head -1 | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain3" ]; then
            echo "✓ Found domain: $domain3"
        else
            echo "✗ No domain references found"
        fi
    else
        echo "✗ docker-compose.yml not found"
    fi

    echo ""
    echo "Method 4: Checking Caddyfile for domain"
    if [ -f "Caddyfile" ]; then
        echo "✓ Caddyfile exists"
        local domain4=$(grep -E "^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}" "Caddyfile" 2>/dev/null | head -1 | awk '{print $1}' | xargs)
        if [ -n "$domain4" ]; then
            echo "✓ Found domain: $domain4"
        else
            echo "✗ No domain found in Caddyfile"
        fi
    else
        echo "✗ Caddyfile not found"
    fi

    echo ""
    echo "Method 5: Checking .env file for domain"
    if [ -f ".env" ]; then
        echo "✓ .env exists"
        local domain5=$(grep -E "DOMAIN|NETBIRD_DOMAIN" ".env" 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" | xargs)
        if [ -n "$domain5" ]; then
            echo "✓ Found domain: $domain5"
        else
            echo "✗ No domain found in .env"
        fi
    else
        echo "✗ .env not found"
    fi

    echo ""
    echo "Final Result:"
    echo "============"
    local detected_domain=$(detect_netbird_domain)
    if [ -n "$detected_domain" ]; then
        echo "✓ Domain detection successful: $detected_domain"
        echo ""
        echo "This domain will be used for organizing backups in cloud storage."
        echo "Backup path will be: $RCLONE_REMOTE:/Backups/Netbird/$detected_domain"
    else
        echo "✗ Domain detection failed"
        echo ""
        echo "Backups will be stored using 'unknown-domain' as fallback."
        echo "Backup path will be: $RCLONE_REMOTE:/Backups/Netbird/unknown-domain"
    fi

    echo ""
    read -rp "Press Enter to continue..."
}
